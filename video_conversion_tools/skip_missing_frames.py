#!/usr/bin/env python3
"""
跳过缺失帧，重新合成视频
Skip missing frames and recompose video
"""

import cv2
import os
import glob
import numpy as np
from PIL import Image
from tqdm import tqdm

print("=== 跳过缺失帧重新合成视频 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

def get_original_video_info():
    """获取原始视频信息"""
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    
    if not possible_videos:
        print("❌ 没有找到原始视频")
        return None
    
    original_video = possible_videos[0]
    cap = cv2.VideoCapture(original_video)
    
    if not cap.isOpened():
        print("❌ 无法打开原始视频")
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.release()
    
    return {
        'fps': fps,
        'total_frames': total_frames,
        'duration': duration,
        'width': width,
        'height': height
    }

def load_available_frames():
    """加载所有可用的转换帧，跳过缺失的帧"""
    print("\n🎨 加载可用的转换帧...")
    
    frames_dir = os.path.join(ROOT_DIR, "outputs/video_processing/converted_ghibli_full")
    
    if not os.path.exists(frames_dir):
        print("❌ 转换帧目录不存在")
        return [], []
    
    # 查找所有PNG文件（优先使用PNG）
    png_files = glob.glob(os.path.join(frames_dir, "flux_ghibli_*.png"))
    
    if not png_files:
        print("❌ 没有找到PNG文件")
        return [], []
    
    print(f"📂 找到 {len(png_files)} 个PNG文件")
    
    # 提取帧号并排序
    frame_data = []
    for png_file in png_files:
        try:
            basename = os.path.basename(png_file)
            # 从文件名提取帧号 (flux_ghibli_XXXX.png)
            frame_num = int(basename.split('_')[2].split('.')[0])
            frame_data.append((frame_num, png_file))
        except:
            continue
    
    # 按帧号排序
    frame_data.sort(key=lambda x: x[0])
    
    print(f"✅ 成功解析 {len(frame_data)} 个帧文件")
    
    if frame_data:
        first_frame = frame_data[0][0]
        last_frame = frame_data[-1][0]
        print(f"   帧号范围: {first_frame} - {last_frame}")
        
        # 检查缺失的帧
        all_frame_nums = [x[0] for x in frame_data]
        expected_frames = list(range(first_frame, last_frame + 1))
        missing_frames = [x for x in expected_frames if x not in all_frame_nums]
        
        print(f"   缺失帧数: {len(missing_frames)}")
        if missing_frames:
            print(f"   缺失帧号: {missing_frames}")
    
    return frame_data, missing_frames

def create_video_skip_missing(frame_data, original_info, missing_frames):
    """创建视频，跳过缺失的帧"""
    print(f"\n🎬 创建视频 (跳过 {len(missing_frames)} 个缺失帧)...")
    
    if not frame_data:
        print("❌ 没有可用帧")
        return False
    
    # 加载所有可用帧
    frames = []
    valid_frame_numbers = []
    
    print("📂 加载帧图片...")
    for frame_num, frame_path in tqdm(frame_data, desc="加载帧"):
        try:
            img = Image.open(frame_path)
            # 调整大小为512x512
            img = img.resize((512, 512), Image.LANCZOS)
            frames.append(np.array(img))
            valid_frame_numbers.append(frame_num)
        except Exception as e:
            print(f"⚠️ 加载帧 {frame_num} 失败: {e}")
            continue
    
    if not frames:
        print("❌ 没有成功加载任何帧")
        return False
    
    print(f"✅ 成功加载 {len(frames)} 帧")
    
    # 计算新的视频参数
    available_frames = len(frames)
    original_fps = original_info['fps']
    
    # 方案1: 保持原始帧率，时长会稍微短一些
    new_duration = available_frames / original_fps
    
    print(f"\n📊 新视频参数:")
    print(f"   原始: {original_info['total_frames']} 帧, {original_info['duration']:.2f} 秒, {original_fps} FPS")
    print(f"   新的: {available_frames} 帧, {new_duration:.2f} 秒, {original_fps} FPS")
    print(f"   时长差异: {original_info['duration'] - new_duration:.2f} 秒")
    print(f"   跳过的帧: {missing_frames}")
    
    # 创建输出路径
    output_path = os.path.join(ROOT_DIR, "outputs/video_processing/video_skip_missing_frames.mp4")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, original_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        print("🎥 合成视频...")
        for frame in tqdm(frames, desc="写入帧"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        # 验证输出
        if os.path.exists(output_path):
            cap = cv2.VideoCapture(output_path)
            if cap.isOpened():
                output_fps = cap.get(cv2.CAP_PROP_FPS)
                output_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                output_duration = output_frames / output_fps
                cap.release()
                
                file_size = os.path.getsize(output_path) / (1024*1024)
                
                print(f"\n✅ 视频创建成功!")
                print(f"📁 文件: {output_path}")
                print(f"💾 大小: {file_size:.2f} MB")
                print(f"📊 最终参数:")
                print(f"   帧率: {output_fps} FPS")
                print(f"   帧数: {output_frames}")
                print(f"   时长: {output_duration:.2f} 秒")
                
                # 计算播放速度
                speed_ratio = output_duration / original_info['duration']
                print(f"   相对原视频: {speed_ratio:.3f}x 速度")
                
                if speed_ratio > 0.95:
                    print("🎉 播放速度接近原视频!")
                else:
                    print(f"⚠️ 播放速度比原视频快 {1/speed_ratio:.2f} 倍")
                
                return True
            else:
                print("❌ 无法验证生成的视频")
                return False
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def create_alternative_version(frame_data, original_info, missing_frames):
    """创建替代版本：调整帧率以匹配原始时长"""
    print(f"\n🔄 创建替代版本 (调整帧率匹配时长)...")
    
    available_frames = len(frame_data)
    target_duration = original_info['duration']
    
    # 计算需要的帧率以匹配原始时长
    adjusted_fps = available_frames / target_duration
    
    print(f"📊 替代版本参数:")
    print(f"   帧数: {available_frames}")
    print(f"   目标时长: {target_duration:.2f} 秒")
    print(f"   调整后帧率: {adjusted_fps:.2f} FPS")
    
    # 加载帧
    frames = []
    for frame_num, frame_path in tqdm(frame_data, desc="加载帧"):
        try:
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            frames.append(np.array(img))
        except:
            continue
    
    if not frames:
        print("❌ 没有成功加载任何帧")
        return False
    
    # 创建输出路径
    output_path = os.path.join(ROOT_DIR, "outputs/video_processing/video_adjusted_fps.mp4")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, adjusted_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="写入帧"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 替代版本创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"🎯 时长: {target_duration:.2f} 秒 (与原视频一致)")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 创建替代版本失败: {e}")
        return False

def main():
    """主程序"""
    print("🎯 目标: 跳过缺失的6帧，重新合成视频")
    print("💡 策略: 使用现有的296帧，不重新转换")
    
    # 1. 获取原始视频信息
    original_info = get_original_video_info()
    if not original_info:
        return
    
    print(f"\n📹 原始视频信息:")
    print(f"   帧率: {original_info['fps']} FPS")
    print(f"   帧数: {original_info['total_frames']}")
    print(f"   时长: {original_info['duration']:.2f} 秒")
    
    # 2. 加载可用帧
    frame_data, missing_frames = load_available_frames()
    if not frame_data:
        return
    
    # 3. 创建两个版本的视频
    print(f"\n🎬 创建视频版本:")
    print("1. 保持原始帧率版本 (时长稍短)")
    print("2. 调整帧率版本 (时长匹配)")
    
    # 版本1: 保持原始帧率
    success1 = create_video_skip_missing(frame_data, original_info, missing_frames)
    
    # 版本2: 调整帧率
    success2 = create_alternative_version(frame_data, original_info, missing_frames)
    
    print(f"\n🎉 处理完成!")
    if success1:
        print("✅ 版本1: video_skip_missing_frames.mp4 (保持30fps)")
    if success2:
        print("✅ 版本2: video_adjusted_fps.mp4 (调整帧率匹配时长)")
    
    print(f"\n💡 建议:")
    print("- 先试试版本1，看播放速度是否正常")
    print("- 如果还是太快，试试版本2")
    print("- 两个版本都跳过了缺失的6帧，不需要重新转换")

if __name__ == "__main__":
    main()
