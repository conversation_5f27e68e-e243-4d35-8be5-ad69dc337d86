#!/usr/bin/env python3
"""
重新合成视频 - 确保与原始视频完全一致的播放速度和帧率
Re-compose Video - Ensure identical playback speed and framerate as original
"""

import cv2
import os
import glob
import numpy as np
from PIL import Image
from tqdm import tqdm

print("=== 重新合成视频 - 匹配原始帧率 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

def analyze_original_video():
    """分析原始视频的详细信息"""
    print("🔍 分析原始视频...")
    
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    
    if not possible_videos:
        print("❌ 没有找到原始视频")
        print(f"请确保视频文件在: {os.path.join(ROOT_DIR, 'inputs/animatediff/')}")
        return None
    
    original_video = possible_videos[0]
    print(f"📹 原始视频: {os.path.basename(original_video)}")
    
    cap = cv2.VideoCapture(original_video)
    if not cap.isOpened():
        print("❌ 无法打开原始视频")
        return None
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = total_frames / fps
    
    cap.release()
    
    video_info = {
        'path': original_video,
        'fps': fps,
        'total_frames': total_frames,
        'width': width,
        'height': height,
        'duration': duration
    }
    
    print(f"📊 视频信息:")
    print(f"   分辨率: {width}x{height}")
    print(f"   帧率: {fps} FPS")
    print(f"   总帧数: {total_frames}")
    print(f"   时长: {duration:.2f} 秒")
    
    return video_info

def find_converted_frames():
    """查找转换后的帧图片"""
    print("\n🎨 查找转换后的帧...")
    
    # 可能的帧目录
    possible_dirs = [
        os.path.join(ROOT_DIR, "outputs/video_processing/converted_ghibli_full"),
        os.path.join(ROOT_DIR, "outputs/video_processing/flux_ghibli_full"),
        os.path.join(ROOT_DIR, "outputs/video_processing/flux_ghibli"),
        os.path.join(ROOT_DIR, "outputs/video_processing/flux_ghibli_reprocessed")
    ]
    
    frames_dir = None
    frame_files = []
    
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            # 查找PNG和JPG文件
            png_files = glob.glob(os.path.join(dir_path, "*.png"))
            jpg_files = glob.glob(os.path.join(dir_path, "*.jpg"))
            
            all_files = png_files + jpg_files
            
            if all_files:
                frames_dir = dir_path
                frame_files = sorted(all_files)
                print(f"✅ 找到帧目录: {os.path.basename(dir_path)}")
                print(f"   帧数量: {len(frame_files)}")
                break
    
    if not frame_files:
        print("❌ 没有找到转换后的帧")
        print("请确保以下目录之一包含转换后的帧:")
        for dir_path in possible_dirs:
            print(f"  - {dir_path}")
        return None, []
    
    # 显示帧信息
    first_frame = os.path.basename(frame_files[0])
    last_frame = os.path.basename(frame_files[-1])
    print(f"   帧范围: {first_frame} 到 {last_frame}")
    
    return frames_dir, frame_files

def load_and_prepare_frames(frame_files, target_count, target_size):
    """加载并准备帧，确保数量和尺寸匹配"""
    print(f"\n🖼️ 加载和准备帧...")
    print(f"   目标帧数: {target_count}")
    print(f"   目标尺寸: {target_size[0]}x{target_size[1]}")
    
    current_count = len(frame_files)
    print(f"   当前帧数: {current_count}")
    
    frames = []
    
    # 加载所有可用帧
    print("📂 加载帧图片...")
    for frame_path in tqdm(frame_files, desc="加载帧"):
        try:
            img = Image.open(frame_path)
            # 调整到目标尺寸
            img = img.resize(target_size, Image.LANCZOS)
            # 转换为numpy数组
            frame_array = np.array(img)
            frames.append(frame_array)
        except Exception as e:
            print(f"⚠️ 加载帧失败: {os.path.basename(frame_path)}, {e}")
            continue
    
    if not frames:
        print("❌ 没有成功加载任何帧")
        return []
    
    print(f"✅ 成功加载 {len(frames)} 帧")
    
    # 如果帧数不足，需要扩展
    if len(frames) < target_count:
        print(f"🔧 扩展帧数: {len(frames)} -> {target_count}")
        
        # 计算重复策略
        repeat_factor = target_count // len(frames)
        remainder = target_count % len(frames)
        
        extended_frames = []
        
        # 重复每一帧
        for i, frame in enumerate(frames):
            # 基础重复
            for _ in range(repeat_factor):
                extended_frames.append(frame.copy())
            
            # 额外重复（均匀分布）
            if i < remainder:
                extended_frames.append(frame.copy())
        
        frames = extended_frames[:target_count]
        print(f"✅ 扩展完成，最终帧数: {len(frames)}")
    
    elif len(frames) > target_count:
        print(f"✂️ 截取帧数: {len(frames)} -> {target_count}")
        frames = frames[:target_count]
    
    return frames

def create_video_with_exact_specs(frames, output_path, video_info):
    """使用精确的规格创建视频"""
    print(f"\n🎬 创建匹配原始视频的新视频...")
    
    if not frames:
        print("❌ 没有可用帧")
        return False
    
    fps = video_info['fps']
    total_frames = len(frames)
    expected_duration = video_info['duration']
    actual_duration = total_frames / fps
    
    print(f"📊 视频参数:")
    print(f"   帧数: {total_frames}")
    print(f"   帧率: {fps} FPS")
    print(f"   预期时长: {expected_duration:.2f} 秒")
    print(f"   实际时长: {actual_duration:.2f} 秒")
    print(f"   时长差异: {abs(actual_duration - expected_duration):.2f} 秒")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    try:
        height, width = frames[0].shape[:2]
        print(f"   输出分辨率: {width}x{height}")
        
        # 使用高质量编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        print("🎥 合成视频...")
        for i, frame in enumerate(tqdm(frames, desc="写入帧")):
            # 确保帧格式正确
            if len(frame.shape) == 3:
                # RGB转BGR (OpenCV使用BGR)
                if frame.shape[2] == 3:
                    frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                else:
                    frame_bgr = frame
            else:
                frame_bgr = frame
            
            out.write(frame_bgr)
        
        out.release()
        
        # 验证输出
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            
            # 验证生成的视频
            cap = cv2.VideoCapture(output_path)
            if cap.isOpened():
                output_fps = cap.get(cv2.CAP_PROP_FPS)
                output_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                output_duration = output_frames / output_fps
                cap.release()
                
                print(f"\n✅ 视频创建成功!")
                print(f"📁 文件: {output_path}")
                print(f"💾 大小: {file_size:.2f} MB")
                print(f"🎯 验证结果:")
                print(f"   帧率: {output_fps} FPS (目标: {fps})")
                print(f"   帧数: {output_frames} (目标: {video_info['total_frames']})")
                print(f"   时长: {output_duration:.2f} 秒 (目标: {expected_duration:.2f})")
                
                # 检查是否匹配
                fps_match = abs(output_fps - fps) < 0.1
                duration_match = abs(output_duration - expected_duration) < 0.5
                
                if fps_match and duration_match:
                    print("🎉 视频参数完全匹配原始视频!")
                else:
                    print("⚠️ 视频参数与原始视频有差异")
                
                return True
            else:
                print("❌ 无法验证生成的视频")
                return False
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    print("🎯 目标: 重新合成视频，确保与原始视频完全一致的播放效果")
    print("📋 流程: 分析原始视频 → 加载转换帧 → 匹配参数合成")
    
    # 1. 分析原始视频
    video_info = analyze_original_video()
    if not video_info:
        return
    
    # 2. 查找转换后的帧
    frames_dir, frame_files = find_converted_frames()
    if not frame_files:
        return
    
    # 3. 加载和准备帧
    target_size = (video_info['width'], video_info['height'])
    frames = load_and_prepare_frames(frame_files, video_info['total_frames'], target_size)
    
    if not frames:
        print("❌ 无法准备帧数据")
        return
    
    # 4. 创建输出路径
    output_path = os.path.join(ROOT_DIR, "outputs/video_processing/recomposed_video_matched.mp4")
    
    # 5. 合成视频
    success = create_video_with_exact_specs(frames, output_path, video_info)
    
    if success:
        print(f"\n🎉 视频重新合成完成!")
        print(f"📁 输出文件: {output_path}")
        print(f"🎯 播放效果: 与原始视频完全一致")
        print(f"\n💡 建议:")
        print(f"   1. 播放新视频验证效果")
        print(f"   2. 对比原始视频确认播放速度")
        print(f"   3. 如有问题可调整帧率参数")
    else:
        print(f"\n❌ 视频重新合成失败")

if __name__ == "__main__":
    main()
