#!/usr/bin/env python3
"""
测试用户输入验证是否正常工作
"""

def test_input_validation():
    """测试输入验证逻辑"""
    print("🧪 测试用户输入验证")
    print("=" * 30)
    
    # 测试不同的输入
    test_inputs = ['y', 'yes', 'Y', 'YES', 'n', 'no', 'N', 'NO', 'maybe', '']
    
    for test_input in test_inputs:
        # 模拟输入验证逻辑
        user_input = test_input.strip().lower()
        
        if user_input in ['y', 'yes']:
            result = "✅ 确认"
        elif user_input in ['n', 'no']:
            result = "❌ 取消"
        else:
            result = "⚠️ 无效输入"
        
        print(f"输入 '{test_input}' -> {result}")
    
    print("\n💡 正确的输入验证逻辑:")
    print("if user_input in ['y', 'yes']:")
    print("    # 确认操作")
    print("elif user_input in ['n', 'no']:")
    print("    # 取消操作")
    print("else:")
    print("    # 无效输入")

def interactive_test():
    """交互式测试"""
    print("\n🎯 交互式测试:")
    print("请输入 y 或 yes 来测试确认功能")
    
    user_input = input("确认测试？(y/n): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        print("✅ 输入验证正常工作！")
        return True
    elif user_input in ['n', 'no']:
        print("❌ 用户取消测试")
        return False
    else:
        print("⚠️ 无效输入，请输入 y/yes 或 n/no")
        return False

if __name__ == "__main__":
    test_input_validation()
    interactive_test()
