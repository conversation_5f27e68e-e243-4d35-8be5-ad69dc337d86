#!/bin/bash

# 🎬 视频转动画工具集 - 快速启动脚本
# Video to Anime Conversion Tools - Quick Start Script

echo "🎬 视频转动画工具集"
echo "===================="

# 检查Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 未找到Python，请先安装Python"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 检查是否在正确目录
if [ ! -f "main.py" ]; then
    echo "❌ 请在 video_conversion_tools 目录下运行此脚本"
    exit 1
fi

# 启动主程序
echo "🚀 启动工具集..."
$PYTHON_CMD main.py
