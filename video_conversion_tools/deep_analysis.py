#!/usr/bin/env python3
"""
深度分析转换视频的实际内容
检查是否存在帧跳跃或内容不连续问题
"""

import cv2
import os
import glob
import numpy as np
from PIL import Image

print("=== 深度视频内容分析 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

def analyze_frame_sequence(video_path, sample_count=50):
    """分析视频帧序列的连续性"""
    print(f"\n🔍 深度分析: {os.path.basename(video_path)}")
    
    if not os.path.exists(video_path):
        print("❌ 文件不存在")
        return None
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频")
        return None
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"📊 开始分析 {total_frames} 帧...")
    
    # 采样帧进行分析
    sample_indices = np.linspace(0, total_frames-1, min(sample_count, total_frames), dtype=int)
    
    frame_data = []
    prev_frame = None
    
    for i, frame_idx in enumerate(sample_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if not ret:
            print(f"⚠️ 无法读取帧 {frame_idx}")
            continue
        
        # 计算帧的特征
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 计算帧的平均亮度
        brightness = np.mean(gray)
        
        # 计算帧的变化程度（如果有前一帧）
        change_score = 0
        if prev_frame is not None:
            diff = cv2.absdiff(gray, prev_frame)
            change_score = np.mean(diff)
        
        # 获取时间戳
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC)
        
        frame_info = {
            'index': frame_idx,
            'timestamp': timestamp,
            'brightness': brightness,
            'change_score': change_score,
            'expected_timestamp': frame_idx * (1000 / fps)  # 期望的时间戳
        }
        
        frame_data.append(frame_info)
        prev_frame = gray.copy()
        
        if i % 10 == 0:
            print(f"   分析进度: {i+1}/{len(sample_indices)}")
    
    cap.release()
    
    # 分析结果
    print(f"\n📈 帧序列分析结果:")
    
    # 检查时间戳连续性
    timestamp_issues = 0
    large_changes = 0
    
    for i, frame in enumerate(frame_data):
        timestamp_diff = abs(frame['timestamp'] - frame['expected_timestamp'])
        
        if timestamp_diff > 50:  # 超过50ms差异
            timestamp_issues += 1
        
        if frame['change_score'] > 50:  # 大幅度变化
            large_changes += 1
        
        # 显示前10帧的详细信息
        if i < 10:
            print(f"   帧{frame['index']:3d}: 时间戳={frame['timestamp']:6.1f}ms (期望{frame['expected_timestamp']:6.1f}ms), "
                  f"亮度={frame['brightness']:5.1f}, 变化={frame['change_score']:5.1f}")
    
    print(f"\n🎯 问题检测:")
    print(f"   时间戳异常: {timestamp_issues}/{len(frame_data)} 帧")
    print(f"   大幅变化: {large_changes}/{len(frame_data)} 帧")
    
    # 计算平均帧间变化
    if len(frame_data) > 1:
        avg_change = np.mean([f['change_score'] for f in frame_data[1:]])
        print(f"   平均帧间变化: {avg_change:.2f}")
        
        if avg_change < 5:
            print("   ⚠️ 帧间变化很小，可能存在重复帧")
        elif avg_change > 100:
            print("   ⚠️ 帧间变化很大，可能存在跳跃")
    
    return frame_data

def check_converted_frames_directory():
    """检查转换帧目录的实际情况"""
    print(f"\n🎨 检查转换帧目录...")
    
    possible_dirs = [
        os.path.join(ROOT_DIR, "outputs/video_processing/converted_ghibli_full"),
        os.path.join(ROOT_DIR, "outputs/video_processing/flux_ghibli_full"),
        os.path.join(ROOT_DIR, "outputs/video_processing/flux_ghibli"),
    ]
    
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            files = sorted(glob.glob(os.path.join(dir_path, "*.*")))
            if files:
                print(f"✅ 找到目录: {os.path.basename(dir_path)}")
                print(f"   文件数量: {len(files)}")
                
                # 检查文件名序列
                frame_numbers = []
                for f in files:
                    try:
                        # 尝试从文件名提取帧号
                        basename = os.path.basename(f)
                        # 查找数字
                        import re
                        numbers = re.findall(r'\d+', basename)
                        if numbers:
                            frame_numbers.append(int(numbers[-1]))  # 取最后一个数字
                    except:
                        continue
                
                if frame_numbers:
                    frame_numbers.sort()
                    print(f"   帧号范围: {frame_numbers[0]} - {frame_numbers[-1]}")
                    
                    # 检查连续性
                    missing = []
                    for i in range(frame_numbers[0], frame_numbers[-1] + 1):
                        if i not in frame_numbers:
                            missing.append(i)
                    
                    print(f"   缺失帧数: {len(missing)}")
                    if missing:
                        print(f"   缺失帧号: {missing[:20]}{'...' if len(missing) > 20 else ''}")
                
                # 显示前几个文件
                print(f"   前5个文件:")
                for f in files[:5]:
                    print(f"     {os.path.basename(f)}")
                
                return dir_path, files
    
    print("❌ 没有找到转换帧目录")
    return None, []

def create_corrected_video():
    """创建修正的视频"""
    print(f"\n🔧 尝试创建修正视频...")
    
    # 获取原始视频信息
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    
    if not possible_videos:
        print("❌ 没有找到原始视频")
        return False
    
    original_video = possible_videos[0]
    cap = cv2.VideoCapture(original_video)
    
    if not cap.isOpened():
        print("❌ 无法打开原始视频")
        return False
    
    orig_fps = cap.get(cv2.CAP_PROP_FPS)
    orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    orig_duration = orig_frames / orig_fps
    cap.release()
    
    print(f"📹 原始视频: {orig_fps} FPS, {orig_frames} 帧, {orig_duration:.2f} 秒")
    
    # 查找转换帧
    frames_dir, frame_files = check_converted_frames_directory()
    if not frame_files:
        return False
    
    print(f"🎨 找到 {len(frame_files)} 个转换帧")
    
    # 加载帧
    frames = []
    for frame_path in frame_files:
        try:
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            frames.append(np.array(img))
        except:
            continue
    
    if not frames:
        print("❌ 无法加载帧")
        return False
    
    print(f"✅ 成功加载 {len(frames)} 帧")
    
    # 如果帧数不足，重复帧以匹配原始时长
    if len(frames) < orig_frames:
        print(f"🔧 扩展帧数: {len(frames)} -> {orig_frames}")
        
        # 简单重复策略
        repeat_factor = orig_frames // len(frames)
        remainder = orig_frames % len(frames)
        
        extended_frames = []
        for i, frame in enumerate(frames):
            for _ in range(repeat_factor):
                extended_frames.append(frame)
            if i < remainder:
                extended_frames.append(frame)
        
        frames = extended_frames[:orig_frames]
        print(f"✅ 最终帧数: {len(frames)}")
    
    # 创建视频 - 使用原始帧率
    output_path = os.path.join(ROOT_DIR, "outputs/video_processing/corrected_video.mp4")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, orig_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        print(f"🎬 创建修正视频: {orig_fps} FPS, {len(frames)} 帧")
        
        for frame in frames:
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            # 验证新视频
            cap = cv2.VideoCapture(output_path)
            if cap.isOpened():
                new_fps = cap.get(cv2.CAP_PROP_FPS)
                new_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                new_duration = new_frames / new_fps
                cap.release()
                
                print(f"✅ 修正视频创建成功!")
                print(f"📁 文件: {output_path}")
                print(f"📊 参数: {new_fps} FPS, {new_frames} 帧, {new_duration:.2f} 秒")
                
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    print("🎯 目标: 深度分析转换视频的实际内容")
    
    # 1. 分析原始视频
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    
    if possible_videos:
        original_video = possible_videos[0]
        print("📹 分析原始视频帧序列...")
        analyze_frame_sequence(original_video, 30)
    
    # 2. 分析转换视频
    converted_video = os.path.join(ROOT_DIR, "outputs/video_processing/full_converted_ghibli_video.mp4")
    if os.path.exists(converted_video):
        print("\n🎨 分析转换视频帧序列...")
        analyze_frame_sequence(converted_video, 30)
    
    # 3. 检查转换帧目录
    check_converted_frames_directory()
    
    # 4. 询问是否创建修正视频
    print(f"\n🤔 是否尝试创建修正视频？")
    choice = input("输入 y 创建修正视频: ").strip().lower()
    
    if choice in ['y', 'yes']:
        create_corrected_video()

if __name__ == "__main__":
    main()
