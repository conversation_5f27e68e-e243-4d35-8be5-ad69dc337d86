#!/usr/bin/env python3
"""
对比原始视频和转换后视频的详细信息
找出播放速度过快的原因
"""

import cv2
import os
import glob

print("=== 视频信息对比分析 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

def analyze_video_detailed(video_path, video_name):
    """详细分析视频信息"""
    print(f"\n🔍 分析 {video_name}:")
    print(f"📁 路径: {video_path}")
    
    if not os.path.exists(video_path):
        print("❌ 文件不存在")
        return None
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频")
        return None
    
    # 获取所有可能的视频属性
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # 计算时长
    duration = frame_count / fps if fps > 0 else 0
    
    # 获取编码信息
    fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
    fourcc_str = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
    
    # 获取文件大小
    file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
    
    # 尝试读取几帧来验证
    actual_frame_count = 0
    frame_timestamps = []
    
    print("📊 正在验证实际帧数...")
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        actual_frame_count += 1
        
        # 记录前10帧的时间戳
        if actual_frame_count <= 10:
            timestamp = cap.get(cv2.CAP_PROP_POS_MSEC)
            frame_timestamps.append(timestamp)
        
        # 为了节省时间，只检查前100帧
        if actual_frame_count >= 100:
            # 跳到最后检查总帧数
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count - 10)
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                actual_frame_count += 1
            break
    
    cap.release()
    
    # 计算实际帧率（基于时间戳）
    if len(frame_timestamps) > 1:
        time_diff = frame_timestamps[-1] - frame_timestamps[0]
        frames_diff = len(frame_timestamps) - 1
        calculated_fps = frames_diff / (time_diff / 1000) if time_diff > 0 else fps
    else:
        calculated_fps = fps
    
    video_info = {
        'path': video_path,
        'fps': fps,
        'calculated_fps': calculated_fps,
        'frame_count': frame_count,
        'actual_frame_count': actual_frame_count,
        'width': width,
        'height': height,
        'duration': duration,
        'fourcc': fourcc_str,
        'file_size': file_size,
        'frame_timestamps': frame_timestamps[:5]  # 只保留前5个
    }
    
    print(f"📺 基本信息:")
    print(f"   分辨率: {width}x{height}")
    print(f"   报告帧率: {fps:.2f} FPS")
    print(f"   计算帧率: {calculated_fps:.2f} FPS")
    print(f"   报告帧数: {frame_count}")
    print(f"   实际帧数: {actual_frame_count} (前100帧检查)")
    print(f"   报告时长: {duration:.2f} 秒")
    print(f"   编码格式: {fourcc_str}")
    print(f"   文件大小: {file_size:.2f} MB")
    
    if frame_timestamps:
        print(f"   前5帧时间戳: {[f'{t:.1f}ms' for t in frame_timestamps[:5]]}")
    
    return video_info

def compare_videos(original_info, converted_info):
    """对比两个视频的信息"""
    print(f"\n📊 详细对比分析:")
    print("=" * 60)
    
    if not original_info or not converted_info:
        print("❌ 无法进行对比，缺少视频信息")
        return
    
    # 对比表格
    comparisons = [
        ("分辨率", f"{original_info['width']}x{original_info['height']}", f"{converted_info['width']}x{converted_info['height']}"),
        ("报告帧率", f"{original_info['fps']:.2f} FPS", f"{converted_info['fps']:.2f} FPS"),
        ("计算帧率", f"{original_info['calculated_fps']:.2f} FPS", f"{converted_info['calculated_fps']:.2f} FPS"),
        ("报告帧数", f"{original_info['frame_count']}", f"{converted_info['frame_count']}"),
        ("报告时长", f"{original_info['duration']:.2f} 秒", f"{converted_info['duration']:.2f} 秒"),
        ("编码格式", original_info['fourcc'], converted_info['fourcc']),
        ("文件大小", f"{original_info['file_size']:.2f} MB", f"{converted_info['file_size']:.2f} MB"),
    ]
    
    print(f"{'属性':<12} {'原始视频':<20} {'转换视频':<20} {'状态'}")
    print("-" * 60)
    
    for attr, orig_val, conv_val in comparisons:
        if orig_val == conv_val:
            status = "✅ 匹配"
        else:
            status = "❌ 不匹配"
        print(f"{attr:<12} {orig_val:<20} {conv_val:<20} {status}")
    
    # 详细分析问题
    print(f"\n🔍 问题分析:")
    
    # 帧率问题
    fps_ratio = converted_info['fps'] / original_info['fps'] if original_info['fps'] > 0 else 1
    if abs(fps_ratio - 1) > 0.1:
        print(f"⚠️ 帧率不匹配:")
        print(f"   原始: {original_info['fps']:.2f} FPS")
        print(f"   转换: {converted_info['fps']:.2f} FPS")
        print(f"   比例: {fps_ratio:.2f}x")
        if fps_ratio > 1:
            print(f"   结果: 播放速度快了 {fps_ratio:.1f} 倍")
        else:
            print(f"   结果: 播放速度慢了 {1/fps_ratio:.1f} 倍")
    
    # 帧数问题
    frame_ratio = converted_info['frame_count'] / original_info['frame_count'] if original_info['frame_count'] > 0 else 1
    if abs(frame_ratio - 1) > 0.1:
        print(f"⚠️ 帧数不匹配:")
        print(f"   原始: {original_info['frame_count']} 帧")
        print(f"   转换: {converted_info['frame_count']} 帧")
        print(f"   比例: {frame_ratio:.2f}x")
        if frame_ratio < 1:
            print(f"   结果: 丢失了 {original_info['frame_count'] - converted_info['frame_count']} 帧")
    
    # 时长问题
    duration_ratio = converted_info['duration'] / original_info['duration'] if original_info['duration'] > 0 else 1
    if abs(duration_ratio - 1) > 0.1:
        print(f"⚠️ 时长不匹配:")
        print(f"   原始: {original_info['duration']:.2f} 秒")
        print(f"   转换: {converted_info['duration']:.2f} 秒")
        print(f"   比例: {duration_ratio:.2f}x")
        if duration_ratio < 1:
            print(f"   结果: 视频短了 {original_info['duration'] - converted_info['duration']:.2f} 秒")
    
    # 播放速度分析
    print(f"\n🎯 播放速度分析:")
    
    # 理论播放速度 = 帧数 / 时长
    orig_playback_speed = original_info['frame_count'] / original_info['duration'] if original_info['duration'] > 0 else 0
    conv_playback_speed = converted_info['frame_count'] / converted_info['duration'] if converted_info['duration'] > 0 else 0
    
    speed_ratio = conv_playback_speed / orig_playback_speed if orig_playback_speed > 0 else 1
    
    print(f"   原始播放速度: {orig_playback_speed:.2f} 帧/秒")
    print(f"   转换播放速度: {conv_playback_speed:.2f} 帧/秒")
    print(f"   速度比例: {speed_ratio:.2f}x")
    
    if speed_ratio > 1.1:
        print(f"🚨 问题确认: 转换视频播放速度快了 {speed_ratio:.1f} 倍!")
    elif speed_ratio < 0.9:
        print(f"🚨 问题确认: 转换视频播放速度慢了 {1/speed_ratio:.1f} 倍!")
    else:
        print(f"✅ 播放速度正常")
    
    # 修复建议
    print(f"\n💡 修复建议:")
    
    if converted_info['frame_count'] < original_info['frame_count']:
        print(f"1. 帧数不足问题:")
        print(f"   - 原因: 转换时丢失了帧")
        print(f"   - 建议: 重新转换或插值补充帧")
    
    if abs(converted_info['fps'] - original_info['fps']) > 0.1:
        print(f"2. 帧率设置问题:")
        print(f"   - 原因: 合成时使用了错误的帧率")
        print(f"   - 建议: 重新合成，使用 {original_info['fps']:.2f} FPS")
    
    if converted_info['duration'] < original_info['duration'] * 0.9:
        print(f"3. 时长不足问题:")
        print(f"   - 原因: 帧数不足或帧率过高")
        print(f"   - 建议: 调整帧率为 {converted_info['frame_count'] / original_info['duration']:.2f} FPS")

def main():
    """主程序"""
    print("🎯 目标: 找出转换视频播放速度过快的原因")
    
    # 1. 查找原始视频
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    
    if not possible_videos:
        print("❌ 没有找到原始视频")
        return
    
    original_video = possible_videos[0]
    
    # 2. 转换后的视频路径
    converted_video = os.path.join(ROOT_DIR, "outputs/video_processing/full_converted_ghibli_video.mp4")
    
    # 3. 分析两个视频
    original_info = analyze_video_detailed(original_video, "原始视频")
    converted_info = analyze_video_detailed(converted_video, "转换视频")
    
    # 4. 对比分析
    compare_videos(original_info, converted_info)
    
    print(f"\n🔧 下一步:")
    print(f"根据上述分析结果，可以:")
    print(f"1. 使用 recompose_video.py 重新合成")
    print(f"2. 手动调整帧率参数")
    print(f"3. 补充缺失的帧")

if __name__ == "__main__":
    main()
