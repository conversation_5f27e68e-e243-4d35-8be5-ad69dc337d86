import replicate
import os
import glob
import requests
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm
import time

print("=== 完整视频转换 - 保持原始帧数和流畅度 ===")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)
WORK_DIR = os.path.join(ROOT_DIR, "outputs/video_processing")

def get_video_info(video_path):
    """获取视频详细信息"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.release()
    
    return {
        'fps': fps,
        'total_frames': total_frames,
        'duration': duration,
        'width': width,
        'height': height
    }

def extract_all_frames(video_path, output_dir):
    """提取视频的每一帧 - 不跳过任何帧"""
    os.makedirs(output_dir, exist_ok=True)
    
    video_info = get_video_info(video_path)
    if not video_info:
        print("❌ 无法读取视频信息")
        return False
    
    print(f"📹 视频信息:")
    print(f"   分辨率: {video_info['width']}x{video_info['height']}")
    print(f"   帧率: {video_info['fps']} FPS")
    print(f"   总帧数: {video_info['total_frames']}")
    print(f"   时长: {video_info['duration']:.2f} 秒")
    
    print(f"\n🎬 提取所有 {video_info['total_frames']} 帧...")
    print("💡 目标: 保持每一帧以确保转换后视频流畅度")
    
    cap = cv2.VideoCapture(video_path)
    frame_count = 0
    success_count = 0
    
    with tqdm(total=video_info['total_frames'], desc="提取帧", unit="帧") as pbar:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 调整大小为512x512以适合AI处理，但保持宽高比
            h, w = frame.shape[:2]
            if h > w:
                new_h, new_w = 512, int(512 * w / h)
            else:
                new_h, new_w = int(512 * h / w), 512
            
            frame_resized = cv2.resize(frame, (new_w, new_h))
            
            # 如果不是正方形，填充到512x512
            if new_h != 512 or new_w != 512:
                frame_padded = np.zeros((512, 512, 3), dtype=np.uint8)
                y_offset = (512 - new_h) // 2
                x_offset = (512 - new_w) // 2
                frame_padded[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = frame_resized
                frame_resized = frame_padded
            
            frame_filename = f"frame_{frame_count:04d}.png"
            frame_path = os.path.join(output_dir, frame_filename)
            
            if cv2.imwrite(frame_path, frame_resized):
                success_count += 1
            
            pbar.update(1)
    
    cap.release()
    
    print(f"\n✅ 帧提取完成:")
    print(f"   成功提取: {success_count}/{video_info['total_frames']} 帧")
    print(f"   成功率: {success_count/video_info['total_frames']*100:.1f}%")
    
    return success_count == video_info['total_frames'], video_info

def save_flux_output(output, output_path):
    """保存 FLUX 输出"""
    try:
        if hasattr(output, 'url'):
            response = requests.get(output.url, timeout=60)
        elif isinstance(output, str) and output.startswith('http'):
            response = requests.get(output, timeout=60)
        else:
            return False
        
        if response.status_code == 200:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return True
        return False
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def convert_all_frames(frames_dir, output_dir, style="ghibli"):
    """转换所有帧 - 完整处理"""
    
    os.makedirs(output_dir, exist_ok=True)
    frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))
    
    if not frame_files:
        print("❌ 没有找到帧文件")
        return []
    
    total_frames = len(frame_files)
    
    print(f"\n🎨 开始转换所有 {total_frames} 帧")
    print(f"🎯 目标: 完整转换，保持原始视频流畅度")
    print(f"💰 预估成本: ${total_frames * 0.01:.2f}")
    print(f"⏱️ 预估时间: {total_frames * 8 / 60:.0f} 分钟 ({total_frames * 8 / 3600:.1f} 小时)")
    
    # 最终确认
    print(f"\n⚠️ 重要提醒:")
    print(f"   - 这将处理所有 {total_frames} 帧")
    print(f"   - 不会跳过或采样任何帧")
    print(f"   - 确保转换后视频与原视频时长一致")
    
    final_confirm = input(f"\n最终确认转换所有 {total_frames} 帧？(yes/no): ").strip().lower()
    if final_confirm != 'yes':
        print("❌ 用户取消转换")
        return []
    
    # 检查已处理的帧
    existing_files = glob.glob(os.path.join(output_dir, f"flux_{style}_*.png"))
    start_frame = len(existing_files)
    
    if start_frame > 0:
        print(f"\n🔄 发现已处理 {start_frame} 帧")
        print(f"📊 进度: {start_frame}/{total_frames} ({start_frame/total_frames*100:.1f}%)")
        
        continue_choice = input("是否从断点继续？(y/n): ").strip().lower()
        if continue_choice != 'y':
            start_frame = 0
            print("🔄 从头开始处理")
    
    # 风格提示词
    style_prompts = {
        "ghibli": "Transform this into Studio Ghibli anime style, Hayao Miyazaki art, soft watercolor painting, magical atmosphere, hand-drawn animation, whimsical, detailed background",
        "shinkai": "Convert this to Makoto Shinkai anime style, Your Name movie style, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic composition",
        "anime": "Make this into high quality anime style, cel shading, vibrant colors, traditional Japanese animation, detailed character art, clean lines",
    }
    
    selected_prompt = style_prompts.get(style, style_prompts["ghibli"])
    print(f"\n🎨 转换风格: {style.upper()}")
    print(f"💬 提示词: {selected_prompt}")
    
    processed_frames = []
    success_count = start_frame
    failed_frames = []
    
    # 处理所有帧
    with tqdm(total=total_frames, initial=start_frame, desc="AI转换", unit="帧") as pbar:
        for i in range(start_frame, total_frames):
            frame_path = frame_files[i]
            
            try:
                # API 调用
                output = replicate.run(
                    "black-forest-labs/flux-kontext-pro",
                    input={
                        "prompt": selected_prompt,
                        "input_image": open(frame_path, "rb"),
                        "aspect_ratio": "match_input_image",
                        "output_format": "jpg",
                        "safety_tolerance": 2
                    }
                )
                
                if output:
                    output_filename = f"flux_{style}_{i+1:04d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    if save_flux_output(output, output_path):
                        # 转换为PNG
                        try:
                            img = Image.open(output_path)
                            img = img.resize((512, 512), Image.LANCZOS)
                            png_path = output_path.replace('.jpg', '.png')
                            img.save(png_path)
                            
                            success_count += 1
                            pbar.set_postfix({
                                '成功': f"{success_count}/{total_frames}",
                                '成功率': f"{success_count/(i+1)*100:.1f}%"
                            })
                            
                        except Exception as e:
                            failed_frames.append(i+1)
                            pbar.set_postfix({'失败': len(failed_frames)})
                    else:
                        failed_frames.append(i+1)
                else:
                    failed_frames.append(i+1)
                
                # API限制等待
                time.sleep(3)
                
            except Exception as e:
                failed_frames.append(i+1)
                error_str = str(e).lower()
                if "rate limit" in error_str:
                    print(f"\n⏳ API限制，等待60秒...")
                    time.sleep(60)
                elif "timeout" in error_str:
                    print(f"\n⏳ 超时，等待10秒...")
                    time.sleep(10)
                else:
                    time.sleep(5)
            
            pbar.update(1)
            
            # 每50帧显示详细统计
            if (i + 1) % 50 == 0:
                success_rate = success_count / (i + 1) * 100
                remaining = total_frames - (i + 1)
                eta_minutes = remaining * 8 / 60
                
                print(f"\n📊 进度报告 ({i+1}/{total_frames}):")
                print(f"   成功率: {success_rate:.1f}%")
                print(f"   失败数: {len(failed_frames)}")
                print(f"   预计剩余: {eta_minutes:.0f} 分钟")
    
    # 加载所有成功转换的帧
    print(f"\n📋 加载转换结果...")
    all_converted_files = sorted(glob.glob(os.path.join(output_dir, f"flux_{style}_*.png")))
    
    for png_file in tqdm(all_converted_files, desc="加载帧"):
        try:
            img = Image.open(png_file)
            processed_frames.append(np.array(img))
        except Exception as e:
            print(f"⚠️ 加载失败: {png_file}")
    
    # 最终统计
    print(f"\n🎯 转换完成统计:")
    print(f"   原始帧数: {total_frames}")
    print(f"   成功转换: {len(processed_frames)}")
    print(f"   转换率: {len(processed_frames)/total_frames*100:.1f}%")
    print(f"   失败帧数: {len(failed_frames)}")
    
    if failed_frames:
        print(f"   失败帧号: {failed_frames[:20]}{'...' if len(failed_frames) > 20 else ''}")
    
    return processed_frames

def create_final_video(frames, output_path, video_info):
    """创建最终视频 - 保持原始参数"""
    if len(frames) == 0:
        print("❌ 没有转换成功的帧")
        return False
    
    fps = video_info['fps']
    duration = len(frames) / fps
    
    print(f"\n🎬 创建最终视频:")
    print(f"   帧数: {len(frames)}")
    print(f"   帧率: {fps} FPS (与原视频一致)")
    print(f"   时长: {duration:.2f} 秒")
    print(f"   目标时长: {video_info['duration']:.2f} 秒")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="合成视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"\n✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            print(f"🎯 帧率: {fps} FPS")
            
            # 验证时长
            if abs(duration - video_info['duration']) < 0.5:
                print(f"✅ 时长匹配原视频!")
            else:
                print(f"⚠️ 时长与原视频有差异")
            
            return True
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序 - 完整视频转换流程"""
    
    print("🎯 目标: 将原视频完整转换为动画风格")
    print("📋 流程: 提取所有帧 → 转换所有帧 → 合成完整视频")
    print("⚠️ 注意: 不会跳过或采样任何帧，确保最佳质量")
    
    # 1. 找到原始视频
    video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
    possible_videos = glob.glob(video_pattern)
    if not possible_videos:
        print("❌ 没有找到原始视频")
        print(f"请确保视频文件在: {os.path.join(ROOT_DIR, 'inputs/animatediff/')}")
        return
    
    original_video = possible_videos[0]
    print(f"\n📹 原始视频: {os.path.basename(original_video)}")
    
    # 2. 检查API Token
    if not os.environ.get("REPLICATE_API_TOKEN"):
        print("❌ 请设置 REPLICATE_API_TOKEN 环境变量")
        return
    
    # 3. 提取所有帧
    frames_dir = f"{WORK_DIR}/frames_full_conversion"
    print(f"\n🚀 步骤1: 提取所有帧")
    
    success, video_info = extract_all_frames(original_video, frames_dir)
    if not success:
        print("❌ 帧提取失败")
        return
    
    # 4. 转换所有帧
    style = "ghibli"
    output_dir = f"{WORK_DIR}/converted_{style}_full"
    
    print(f"\n🚀 步骤2: 转换所有帧为 {style.upper()} 风格")
    converted_frames = convert_all_frames(frames_dir, output_dir, style)
    
    if not converted_frames:
        print("❌ 没有成功转换任何帧")
        return
    
    # 5. 创建最终视频
    output_video = f"{WORK_DIR}/full_converted_{style}_video.mp4"
    
    print(f"\n🚀 步骤3: 创建最终视频")
    success = create_final_video(converted_frames, output_video, video_info)
    
    if success:
        print(f"\n🎉 完整视频转换成功!")
        print(f"🎬 输出文件: {output_video}")
        print(f"📊 转换统计: {len(converted_frames)}/{video_info['total_frames']} 帧")
        print(f"🎯 质量: 保持原始流畅度和时长")
    else:
        print(f"\n❌ 视频创建失败")

if __name__ == "__main__":
    main()
