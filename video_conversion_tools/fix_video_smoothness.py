import os
import glob
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm

print("=== 修复视频帧率和平滑度 ===")

WORK_DIR = "outputs/video_processing"

def analyze_original_video():
    """分析原始视频的帧率信息"""
    
    original_video = None
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    
    if possible_videos:
        original_video = possible_videos[0]
        
        cap = cv2.VideoCapture(original_video)
        if cap.isOpened():
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            print(f"📹 原始视频信息:")
            print(f"   文件: {os.path.basename(original_video)}")
            print(f"   帧率: {fps} FPS")
            print(f"   总帧数: {total_frames}")
            print(f"   时长: {duration:.2f} 秒")
            
            cap.release()
            return fps, total_frames, duration
    
    return 30.0, 151, 5.03  # 默认值

def check_frame_sequence(flux_dir):
    """检查帧序列的连续性"""
    
    flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.png")))
    if not flux_files:
        flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.jpg")))
    
    print(f"🔍 检查帧序列连续性:")
    print(f"   找到 {len(flux_files)} 个转换帧")
    
    frame_numbers = []
    for f in flux_files:
        try:
            base_name = os.path.basename(f)
            frame_num = int(base_name.split('_')[-1].split('.')[0])
            frame_numbers.append(frame_num)
        except:
            continue
    
    frame_numbers.sort()
    
    missing_frames = []
    if frame_numbers:
        expected_range = range(frame_numbers[0], frame_numbers[-1] + 1)
        missing_frames = [i for i in expected_range if i not in frame_numbers]
    
    print(f"   帧范围: {frame_numbers[0] if frame_numbers else 0} - {frame_numbers[-1] if frame_numbers else 0}")
    print(f"   缺失帧数: {len(missing_frames)}")
    
    if missing_frames:
        print(f"   缺失帧号: {missing_frames[:20]}{'...' if len(missing_frames) > 20 else ''}")
    
    return frame_numbers, missing_frames

def interpolate_missing_frames(frames, frame_numbers, missing_frames):
    """插值缺失的帧"""
    
    if not missing_frames:
        return frames, frame_numbers
    
    print(f"🔧 插值 {len(missing_frames)} 个缺失帧...")
    
    # 创建完整的帧序列
    all_numbers = sorted(frame_numbers + missing_frames)
    interpolated_frames = []
    
    for i, frame_num in enumerate(tqdm(all_numbers, desc="插值帧")):
        if frame_num in frame_numbers:
            # 原有帧
            orig_index = frame_numbers.index(frame_num)
            interpolated_frames.append(frames[orig_index])
        else:
            # 需要插值的帧
            # 找到前后最近的帧
            prev_frame = None
            next_frame = None
            
            for j in range(i-1, -1, -1):
                if all_numbers[j] in frame_numbers:
                    prev_index = frame_numbers.index(all_numbers[j])
                    prev_frame = frames[prev_index]
                    break
            
            for j in range(i+1, len(all_numbers)):
                if all_numbers[j] in frame_numbers:
                    next_index = frame_numbers.index(all_numbers[j])
                    next_frame = frames[next_index]
                    break
            
            # 进行插值
            if prev_frame is not None and next_frame is not None:
                # 简单平均插值
                interpolated_frame = (prev_frame.astype(np.float32) + next_frame.astype(np.float32)) / 2
                interpolated_frames.append(interpolated_frame.astype(np.uint8))
            elif prev_frame is not None:
                interpolated_frames.append(prev_frame)
            elif next_frame is not None:
                interpolated_frames.append(next_frame)
            else:
                # 创建黑帧
                interpolated_frames.append(np.zeros_like(frames[0]))
    
    print(f"✅ 插值完成，总帧数: {len(interpolated_frames)}")
    return interpolated_frames, all_numbers

def apply_temporal_smoothing(frames, strength=0.3):
    """应用时间平滑滤波减少闪烁"""
    
    if len(frames) < 3:
        return frames
    
    print(f"🎨 应用时间平滑滤波 (强度: {strength})...")
    
    smoothed_frames = []
    
    for i in tqdm(range(len(frames)), desc="平滑处理"):
        if i == 0:
            # 第一帧
            smoothed_frames.append(frames[i])
        elif i == len(frames) - 1:
            # 最后一帧
            smoothed_frames.append(frames[i])
        else:
            # 中间帧：与前后帧混合
            prev_frame = frames[i-1].astype(np.float32)
            curr_frame = frames[i].astype(np.float32)
            next_frame = frames[i+1].astype(np.float32)
            
            # 时间平滑：当前帧 + 少量前后帧
            smoothed = (
                curr_frame * (1 - strength) + 
                (prev_frame + next_frame) * strength / 2
            )
            
            smoothed_frames.append(smoothed.astype(np.uint8))
    
    print(f"✅ 时间平滑完成")
    return smoothed_frames

def expand_frames_to_match_duration(frames, target_frame_count):
    """扩展帧数以匹配目标时长"""
    if len(frames) == 0:
        return []

    current_count = len(frames)

    if current_count >= target_frame_count:
        return frames[:target_frame_count]

    print(f"🔧 扩展帧数: {current_count} -> {target_frame_count}")

    # 计算重复倍数
    repeat_factor = target_frame_count // current_count
    remainder = target_frame_count % current_count

    expanded_frames = []

    # 重复每一帧
    for i, frame in enumerate(frames):
        # 基础重复
        for _ in range(repeat_factor):
            expanded_frames.append(frame)

        # 额外重复（分布在前面的帧中）
        if i < remainder:
            expanded_frames.append(frame)

    return expanded_frames[:target_frame_count]

def create_matched_framerate_video(frames, output_path, original_fps=30.0, target_duration=None):
    """创建与原视频匹配帧率的视频"""

    if len(frames) == 0:
        print("❌ 没有可用帧")
        return False

    # 计算目标参数
    available_frames = len(frames)

    if target_duration:
        # 计算需要的总帧数
        target_frame_count = int(original_fps * target_duration)

        print(f"\n🎯 匹配目标时长:")
        print(f"   当前帧数: {available_frames}")
        print(f"   目标时长: {target_duration:.2f} 秒")
        print(f"   目标帧率: {original_fps} FPS")
        print(f"   需要帧数: {target_frame_count}")

        # 扩展帧数
        if available_frames < target_frame_count:
            frames = expand_frames_to_match_duration(frames, target_frame_count)
            available_frames = len(frames)

        target_fps = original_fps
    else:
        # 使用原始帧率
        target_fps = original_fps

    actual_duration = available_frames / target_fps

    print(f"\n🎬 创建匹配帧率视频:")
    print(f"   最终帧数: {available_frames}")
    print(f"   目标帧率: {target_fps:.2f} FPS")
    print(f"   视频时长: {actual_duration:.2f} 秒")

    try:
        height, width = frames[0].shape[:2]

        # 使用更好的编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))

        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False

        # 写入所有帧
        for frame in tqdm(frames, desc="生成视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)

        out.release()

        # 验证输出
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"\n✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {actual_duration:.2f} 秒")
            print(f"🎯 帧率: {target_fps:.2f} FPS")
            return True
        else:
            print("❌ 视频文件未生成")
            return False

    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def load_frames_in_order(flux_dir):
    """按顺序加载帧"""
    
    flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.png")))
    if not flux_files:
        flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.jpg")))
    
    frames = []
    frame_numbers = []
    
    for frame_path in tqdm(flux_files, desc="加载帧"):
        try:
            # 提取帧号
            base_name = os.path.basename(frame_path)
            frame_num = int(base_name.split('_')[-1].split('.')[0])
            
            # 加载图像
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            
            frames.append(np.array(img))
            frame_numbers.append(frame_num)
            
        except Exception as e:
            print(f"⚠️ 加载失败: {frame_path}, {e}")
            continue
    
    return frames, frame_numbers

def main():
    """主程序"""
    
    # 分析原始视频
    original_fps, original_frames, original_duration = analyze_original_video()
    
    # 找到FLUX转换目录
    possible_dirs = [
        f"{WORK_DIR}/flux_ghibli",
        f"{WORK_DIR}/flux_ghibli_full",
        f"{WORK_DIR}/flux_anime"
    ]
    
    flux_dir = None
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            files = glob.glob(os.path.join(dir_path, "flux_ghibli_*.*"))
            if files:
                flux_dir = dir_path
                print(f"✅ 使用目录: {flux_dir}")
                break
    
    if not flux_dir:
        print("❌ 没有找到FLUX转换帧")
        return
    
    # 检查帧序列
    frame_numbers, missing_frames = check_frame_sequence(flux_dir)
    
    # 加载帧
    frames, frame_numbers = load_frames_in_order(flux_dir)
    
    if not frames:
        print("❌ 无法加载帧")
        return
    
    print(f"\n🎯 处理选项:")
    print("1. 修复缺失帧 + 时间平滑 (推荐)")
    print("2. 仅时间平滑")
    print("3. 直接创建视频")
    
    choice = input("\n选择处理方式 (1-3): ").strip()
    
    processed_frames = frames
    
    if choice == "1":
        # 插值缺失帧
        if missing_frames:
            processed_frames, _ = interpolate_missing_frames(frames, frame_numbers, missing_frames)
        
        # 应用时间平滑
        processed_frames = apply_temporal_smoothing(processed_frames, strength=0.3)
        
    elif choice == "2":
        # 仅时间平滑
        processed_frames = apply_temporal_smoothing(frames, strength=0.3)
    
    # 创建不同版本的视频
    print(f"\n📹 创建视频版本...")
    
    # 版本1: 匹配原始时长
    output_matched = f"{WORK_DIR}/part1_ghibli_matched_duration.mp4"
    create_matched_framerate_video(
        processed_frames, 
        output_matched, 
        target_duration=original_duration
    )
    
    # 版本2: 标准30fps
    output_30fps = f"{WORK_DIR}/part1_ghibli_30fps.mp4"
    create_matched_framerate_video(
        processed_frames, 
        output_30fps, 
        original_fps=30.0
    )
    
    # 版本3: 较慢的24fps
    output_24fps = f"{WORK_DIR}/part1_ghibli_24fps.mp4"
    create_matched_framerate_video(
        processed_frames, 
        output_24fps, 
        original_fps=24.0
    )
    
    print(f"\n🎉 所有版本创建完成!")
    print(f"📁 匹配时长版本: {output_matched}")
    print(f"📁 30fps 版本: {output_30fps}")
    print(f"📁 24fps 版本: {output_24fps}")

if __name__ == "__main__":
    main()