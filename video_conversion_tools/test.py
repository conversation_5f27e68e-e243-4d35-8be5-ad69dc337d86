import cv2
import os
import glob
from PIL import Image
import numpy as np
from tqdm import tqdm

print("=== 视频帧率问题诊断与修复 ===")

def diagnose_frame_mismatch():
    """诊断帧率不匹配问题"""

    # 1. 分析原始视频
    original_video = None
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    if possible_videos:
        original_video = possible_videos[0]

        cap = cv2.VideoCapture(original_video)
        if cap.isOpened():
            orig_fps = cap.get(cv2.CAP_PROP_FPS)
            orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            orig_duration = orig_frames / orig_fps

            print(f"📹 原始视频:")
            print(f"   帧率: {orig_fps} FPS")
            print(f"   总帧数: {orig_frames}")
            print(f"   时长: {orig_duration:.2f} 秒")
            cap.release()

            return orig_fps, orig_frames, orig_duration
        else:
            print("❌ 无法读取原始视频")
            return None, None, None

    return None, None, None

def analyze_converted_frames():
    """分析转换后的帧"""

    # 2. 分析提取的原始帧
    frames_dir = "outputs/video_processing/frames_part1"
    extracted_count = 0
    if os.path.exists(frames_dir):
        original_frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))
        extracted_count = len(original_frame_files)
        print(f"\n📂 提取的原始帧:")
        print(f"   数量: {extracted_count}")

        if original_frame_files:
            first_frame = os.path.basename(original_frame_files[0])
            last_frame = os.path.basename(original_frame_files[-1])
            print(f"   范围: {first_frame} 到 {last_frame}")

    # 3. 分析转换后的帧
    flux_dirs = [
        "outputs/video_processing/flux_ghibli",
        "outputs/video_processing/flux_ghibli_full"
    ]

    converted_frames = []
    converted_count = 0

    for flux_dir in flux_dirs:
        if os.path.exists(flux_dir):
            flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.png")))
            if not flux_files:
                flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.jpg")))

            if flux_files:
                converted_count = len(flux_files)
                print(f"\n🎨 转换后的帧 ({os.path.basename(flux_dir)}):")
                print(f"   数量: {converted_count}")

                first_converted = os.path.basename(flux_files[0])
                last_converted = os.path.basename(flux_files[-1])
                print(f"   范围: {first_converted} 到 {last_converted}")

                # 检查连续性
                frame_numbers = []
                for f in flux_files:
                    try:
                        base_name = os.path.basename(f)
                        num = int(base_name.split('_')[-1].split('.')[0])
                        frame_numbers.append(num)
                    except:
                        continue

                if frame_numbers:
                    frame_numbers.sort()
                    missing = []
                    for i in range(frame_numbers[0], frame_numbers[-1] + 1):
                        if i not in frame_numbers:
                            missing.append(i)

                    print(f"   帧号范围: {frame_numbers[0]} - {frame_numbers[-1]}")
                    print(f"   缺失帧数: {len(missing)}")

                    if missing:
                        print(f"   缺失帧号: {missing[:20]}{'...' if len(missing) > 20 else ''}")

                    # 计算如果按不同帧率播放的时长
                    for fps in [12, 24, 30]:
                        duration = len(flux_files) / fps
                        print(f"   {fps}fps播放时长: {duration:.2f}秒")

                # 加载帧用于修复
                for frame_path in flux_files:
                    try:
                        img = Image.open(frame_path)
                        img = img.resize((512, 512), Image.LANCZOS)
                        converted_frames.append(np.array(img))
                    except:
                        continue

                break

    return converted_frames, extracted_count, converted_count

def create_quick_fix_video(frames, orig_fps, orig_duration, output_path):
    """快速修复视频帧率"""
    if len(frames) == 0:
        print("❌ 没有可用帧")
        return False

    # 计算需要的总帧数
    target_frame_count = int(orig_fps * orig_duration)
    current_count = len(frames)

    print(f"\n🔧 快速修复:")
    print(f"   当前帧数: {current_count}")
    print(f"   目标帧数: {target_frame_count}")
    print(f"   重复倍数: {target_frame_count // current_count}")

    # 重复帧以匹配时长
    fixed_frames = []
    repeat_factor = max(1, target_frame_count // current_count)

    for frame in frames:
        for _ in range(repeat_factor):
            fixed_frames.append(frame)

    # 补充到目标帧数
    while len(fixed_frames) < target_frame_count:
        fixed_frames.append(frames[-1])

    # 截断到目标帧数
    fixed_frames = fixed_frames[:target_frame_count]

    print(f"   最终帧数: {len(fixed_frames)}")

    # 创建视频
    try:
        height, width = fixed_frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, orig_fps, (width, height))

        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False

        for frame in tqdm(fixed_frames, desc="生成修复视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)

        out.release()

        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            actual_duration = len(fixed_frames) / orig_fps
            print(f"\n✅ 修复视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {actual_duration:.2f} 秒")
            print(f"🎯 帧率: {orig_fps} FPS")
            return True
        else:
            print("❌ 视频文件未生成")
            return False

    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""

    # 诊断问题
    orig_fps, orig_frames, orig_duration = diagnose_frame_mismatch()

    if not orig_fps:
        print("❌ 无法获取原始视频信息")
        return

    # 分析转换帧
    converted_frames, extracted_count, converted_count = analyze_converted_frames()

    if not converted_frames:
        print("❌ 没有找到转换后的帧")
        return

    # 显示问题总结
    print(f"\n📊 问题总结:")
    print(f"   原始视频: {orig_frames} 帧, {orig_duration:.2f} 秒")
    print(f"   提取帧数: {extracted_count} ({extracted_count/orig_frames*100:.1f}%)")
    print(f"   转换帧数: {converted_count} ({converted_count/orig_frames*100:.1f}%)")
    print(f"   帧率损失: {(1-converted_count/orig_frames)*100:.1f}%")

    # 询问是否修复
    fix_choice = input(f"\n是否创建修复视频？(y/n): ").strip().lower()
    if fix_choice == 'y':
        output_path = "outputs/video_processing/quick_fixed_video.mp4"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        success = create_quick_fix_video(converted_frames, orig_fps, orig_duration, output_path)

        if success:
            print(f"\n🎉 修复完成! 请查看: {output_path}")
        else:
            print(f"\n❌ 修复失败")

if __name__ == "__main__":
    main()