#!/usr/bin/env python3
"""
修复所有脚本中的用户输入验证问题
"""

import os
import re

def fix_input_validation_in_file(file_path):
    """修复单个文件中的输入验证"""
    if not os.path.exists(file_path):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复 yes/no 输入验证
        patterns = [
            # 修复 final_confirm != 'yes' 类型
            (r"input\(.*\?\(yes/no\).*\)\.strip\(\)\.lower\(\)", 
             lambda m: m.group(0).replace("(yes/no)", "(y/n)")),
            
            (r"if\s+(\w+)\s*!=\s*['\"]yes['\"]:", 
             r"if \1 not in ['y', 'yes']:"),
            
            # 修复其他常见的输入验证问题
            (r"input\(.*确认.*\?\(y/n\).*\)\.strip\(\)\.lower\(\)\s*!=\s*['\"]y['\"]", 
             lambda m: m.group(0).replace("!= 'y'", "not in ['y', 'yes']")),
        ]
        
        for pattern, replacement in patterns:
            if callable(replacement):
                content = re.sub(pattern, replacement, content)
            else:
                content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {os.path.basename(file_path)}")
            return True
        else:
            print(f"⚪ {os.path.basename(file_path)} 无需修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {os.path.basename(file_path)} 失败: {e}")
        return False

def main():
    """主程序"""
    print("🔧 修复用户输入验证问题")
    print("=" * 40)
    
    # 需要检查的Python文件
    script_files = [
        "full_video_conversion.py",
        "step_by_step_fix.py", 
        "test.py",
        "complete_reprocess.py",
        "fix_video_smoothness.py",
        "fix_framerate_issue.py",
        "video_to_anime_uv.py",
        "main.py"
    ]
    
    fixed_count = 0
    
    for script_file in script_files:
        if fix_input_validation_in_file(script_file):
            fixed_count += 1
    
    print("=" * 40)
    print(f"🎯 修复完成: {fixed_count}/{len(script_files)} 个文件被修复")
    
    if fixed_count > 0:
        print("\n💡 修复内容:")
        print("  - 统一输入提示为 (y/n)")
        print("  - 接受 'y' 和 'yes' 作为确认")
        print("  - 修复输入验证逻辑")

if __name__ == "__main__":
    main()
