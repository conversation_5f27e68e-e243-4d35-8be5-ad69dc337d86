#!/usr/bin/env python3
"""
🎬 视频转动画工具集 - 主入口
Video to Anime Conversion Tools - Main Entry Point

使用方法: python main.py
"""

import os
import sys
import subprocess

def print_banner():
    """显示工具集横幅"""
    print("=" * 60)
    print("🎬 视频转动画工具集 Video to Anime Conversion Tools")
    print("=" * 60)
    print("🎯 目标: 将真实视频转换为高质量动画风格")
    print("🔧 功能: 解决帧率不匹配、画面闪烁等问题")
    print("=" * 60)

def check_environment():
    """检查环境配置"""
    print("\n🔍 检查环境配置...")
    
    # 检查Python包
    required_packages = ['cv2', 'PIL', 'numpy', 'tqdm', 'replicate', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            elif package == 'numpy':
                import numpy
            elif package == 'tqdm':
                from tqdm import tqdm
            elif package == 'replicate':
                import replicate
            elif package == 'requests':
                import requests
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少Python包: {', '.join(missing_packages)}")
        print("请运行: pip install opencv-python pillow numpy tqdm replicate requests")
        return False
    
    # 检查API Token
    if not os.environ.get("REPLICATE_API_TOKEN"):
        print("⚠️ 未设置 REPLICATE_API_TOKEN 环境变量")
        print("请设置: export REPLICATE_API_TOKEN='your_token_here'")
        print("注意: 诊断和预览功能不需要API Token")
    else:
        print("✅ REPLICATE_API_TOKEN 已设置")
    
    # 检查目录结构
    required_dirs = ['inputs/animatediff', 'outputs/video_processing']
    for dir_path in required_dirs:
        full_path = os.path.join('..', dir_path)
        if not os.path.exists(full_path):
            print(f"⚠️ 目录不存在: {dir_path}")
            try:
                os.makedirs(full_path, exist_ok=True)
                print(f"✅ 已创建目录: {dir_path}")
            except:
                print(f"❌ 无法创建目录: {dir_path}")
        else:
            print(f"✅ 目录存在: {dir_path}")
    
    print("✅ 环境检查完成")
    return True

def show_tools_menu():
    """显示工具菜单"""
    print("\n🛠️ 可用工具:")
    print()
    
    tools = [
        {
            'key': '1',
            'name': 'step_by_step_fix.py',
            'title': '🔍 智能诊断工具',
            'desc': '分析问题并推荐最佳解决方案',
            'time': '1分钟',
            'cost': '免费',
            'recommend': '首次使用推荐'
        },
        {
            'key': '2', 
            'name': 'full_video_conversion.py',
            'title': '⭐ 完整视频转换',
            'desc': '转换所有帧，保持原始质量和时长',
            'time': '40分钟',
            'cost': '~$3.02',
            'recommend': '最佳质量'
        },
        {
            'key': '3',
            'name': 'test.py', 
            'title': '🧪 快速测试工具',
            'desc': '快速诊断问题并创建预览视频',
            'time': '2分钟',
            'cost': '免费',
            'recommend': '快速预览'
        },
        {
            'key': '4',
            'name': 'complete_reprocess.py',
            'title': '🔄 完整重处理工具', 
            'desc': '重新处理，支持完整或采样模式',
            'time': '10-40分钟',
            'cost': '$1-3',
            'recommend': '灵活处理'
        },
        {
            'key': '5',
            'name': 'fix_video_smoothness.py',
            'title': '🎨 视频平滑工具',
            'desc': '修复闪烁，优化已转换视频',
            'time': '5分钟', 
            'cost': '免费',
            'recommend': '后期优化'
        },
        {
            'key': '6',
            'name': 'fix_framerate_issue.py',
            'title': '⚡ 帧率修复工具',
            'desc': '专门解决帧率不匹配问题',
            'time': '10分钟',
            'cost': '免费', 
            'recommend': '帧率问题'
        },
        {
            'key': '7',
            'name': 'video_to_anime_uv.py',
            'title': '📺 基础转换工具',
            'desc': '原始转换功能，已优化',
            'time': '变化',
            'cost': '变化',
            'recommend': '基础需求'
        }
    ]
    
    for tool in tools:
        print(f"{tool['key']}. {tool['title']}")
        print(f"   文件: {tool['name']}")
        print(f"   功能: {tool['desc']}")
        print(f"   时间: {tool['time']} | 成本: {tool['cost']} | {tool['recommend']}")
        print()
    
    print("8. 📖 查看详细说明 (README.md)")
    print("9. 🚪 退出")
    print()

def show_usage_guide():
    """显示使用指南"""
    print("\n📋 使用指南:")
    print()
    print("🎯 推荐流程:")
    print("  首次使用 → 选择1 (诊断) → 根据推荐选择对应工具")
    print("  快速预览 → 选择3 (测试) → 查看转换效果")
    print("  最佳质量 → 选择2 (完整转换) → 获得完美结果")
    print()
    print("💡 场景选择:")
    print("  • 不知道选什么 → 选择1 (智能诊断)")
    print("  • 要最好质量 → 选择2 (完整转换)")
    print("  • 想快速看效果 → 选择3 (快速测试)")
    print("  • 有转换问题 → 选择5或6 (修复工具)")
    print()

def run_tool(script_name):
    """运行指定的工具"""
    if not os.path.exists(script_name):
        print(f"❌ 文件不存在: {script_name}")
        return False
    
    print(f"\n🚀 启动工具: {script_name}")
    print("=" * 50)
    
    try:
        # 使用当前Python解释器运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              cwd=os.getcwd(),
                              check=False)
        
        print("=" * 50)
        if result.returncode == 0:
            print(f"✅ {script_name} 执行完成")
        else:
            print(f"⚠️ {script_name} 执行结束 (返回码: {result.returncode})")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断了 {script_name}")
        return True
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")
        return False

def show_readme():
    """显示README内容"""
    readme_path = "README.md"
    if os.path.exists(readme_path):
        print("\n📖 详细说明:")
        print("=" * 60)
        try:
            with open(readme_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 只显示前50行，避免输出过长
                lines = content.split('\n')
                for i, line in enumerate(lines[:50]):
                    print(line)
                if len(lines) > 50:
                    print(f"\n... (还有 {len(lines)-50} 行，请直接查看 README.md)")
        except Exception as e:
            print(f"❌ 读取README失败: {e}")
    else:
        print("❌ README.md 文件不存在")

def main():
    """主程序"""
    print_banner()
    
    # 检查环境
    env_ok = check_environment()
    
    while True:
        show_usage_guide()
        show_tools_menu()
        
        choice = input("请选择工具 (1-9): ").strip()
        
        if choice == '1':
            run_tool('step_by_step_fix.py')
        elif choice == '2':
            if not os.environ.get("REPLICATE_API_TOKEN"):
                print("❌ 此工具需要 REPLICATE_API_TOKEN")
                continue
            run_tool('full_video_conversion.py')
        elif choice == '3':
            run_tool('test.py')
        elif choice == '4':
            if not os.environ.get("REPLICATE_API_TOKEN"):
                print("❌ 此工具需要 REPLICATE_API_TOKEN")
                continue
            run_tool('complete_reprocess.py')
        elif choice == '5':
            run_tool('fix_video_smoothness.py')
        elif choice == '6':
            run_tool('fix_framerate_issue.py')
        elif choice == '7':
            if not os.environ.get("REPLICATE_API_TOKEN"):
                print("❌ 此工具需要 REPLICATE_API_TOKEN")
                continue
            run_tool('video_to_anime_uv.py')
        elif choice == '8':
            show_readme()
        elif choice == '9':
            print("\n👋 感谢使用视频转动画工具集！")
            break
        else:
            print("❌ 无效选择，请输入 1-9")
        
        # 询问是否继续
        if choice in ['1', '2', '3', '4', '5', '6', '7']:
            continue_choice = input(f"\n是否继续使用其他工具？(y/n): ").strip().lower()
            if continue_choice != 'y':
                print("\n👋 感谢使用视频转动画工具集！")
                break

if __name__ == "__main__":
    main()
