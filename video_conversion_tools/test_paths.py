#!/usr/bin/env python3
"""
测试路径修复是否正确
"""

import os
import glob

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

print("🔍 路径测试")
print("=" * 40)
print(f"脚本目录: {SCRIPT_DIR}")
print(f"根目录: {ROOT_DIR}")
print()

# 测试视频路径
video_pattern = os.path.join(ROOT_DIR, "inputs/animatediff/*.mp4")
possible_videos = glob.glob(video_pattern)

print(f"🎬 视频文件搜索:")
print(f"搜索路径: {video_pattern}")
print(f"找到文件: {len(possible_videos)}")

if possible_videos:
    for video in possible_videos:
        print(f"  - {os.path.basename(video)}")
        # 检查文件是否存在
        if os.path.exists(video):
            print(f"    ✅ 文件存在")
        else:
            print(f"    ❌ 文件不存在")
else:
    print("  ❌ 没有找到视频文件")
    print(f"  请确保视频文件在: {os.path.join(ROOT_DIR, 'inputs/animatediff/')}")

print()

# 测试输出目录
work_dir = os.path.join(ROOT_DIR, "outputs/video_processing")
print(f"📁 输出目录:")
print(f"路径: {work_dir}")

if os.path.exists(work_dir):
    print("  ✅ 目录存在")
    
    # 检查子目录
    subdirs = [
        "frames_part1",
        "flux_ghibli", 
        "flux_ghibli_full"
    ]
    
    for subdir in subdirs:
        subdir_path = os.path.join(work_dir, subdir)
        if os.path.exists(subdir_path):
            files = glob.glob(os.path.join(subdir_path, "*"))
            print(f"  ✅ {subdir}: {len(files)} 文件")
        else:
            print(f"  ❌ {subdir}: 不存在")
else:
    print("  ❌ 目录不存在")
    try:
        os.makedirs(work_dir, exist_ok=True)
        print("  ✅ 已创建目录")
    except Exception as e:
        print(f"  ❌ 创建失败: {e}")

print()
print("🎯 路径测试完成")
