#!/usr/bin/env python3
"""
视频播放平滑处理 - 解决"幻灯片"效果
Smooth Video Playback - Fix slideshow effect
"""

import cv2
import os
import glob
import numpy as np
from PIL import Image, ImageFilter
from tqdm import tqdm

print("=== 视频播放平滑处理 ===")
print("🎯 目标: 解决快速幻灯片效果，创建流畅视频")

# 获取脚本所在目录的父目录作为工作根目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPT_DIR)

def load_converted_frames():
    """加载转换后的帧"""
    print("\n📂 加载转换帧...")
    
    frames_dir = os.path.join(ROOT_DIR, "outputs/video_processing/converted_ghibli_full")
    
    if not os.path.exists(frames_dir):
        print("❌ 转换帧目录不存在")
        return []
    
    # 查找PNG文件
    png_files = glob.glob(os.path.join(frames_dir, "flux_ghibli_*.png"))
    
    if not png_files:
        print("❌ 没有找到PNG文件")
        return []
    
    # 提取帧号并排序
    frame_data = []
    for png_file in png_files:
        try:
            basename = os.path.basename(png_file)
            frame_num = int(basename.split('_')[2].split('.')[0])
            frame_data.append((frame_num, png_file))
        except:
            continue
    
    frame_data.sort(key=lambda x: x[0])
    
    # 加载帧
    frames = []
    print(f"📊 加载 {len(frame_data)} 个帧...")
    
    for frame_num, frame_path in tqdm(frame_data, desc="加载帧"):
        try:
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            frames.append(np.array(img))
        except:
            continue
    
    print(f"✅ 成功加载 {len(frames)} 帧")
    return frames

def apply_temporal_smoothing(frames, strength=0.3):
    """应用时间平滑 - 减少帧间跳跃"""
    print(f"\n🎨 应用时间平滑 (强度: {strength})...")
    
    if len(frames) < 3:
        return frames
    
    smoothed_frames = []
    
    for i in tqdm(range(len(frames)), desc="时间平滑"):
        if i == 0:
            # 第一帧保持不变
            smoothed_frames.append(frames[i])
        elif i == len(frames) - 1:
            # 最后一帧保持不变
            smoothed_frames.append(frames[i])
        else:
            # 中间帧：与前后帧混合
            prev_frame = frames[i-1].astype(np.float32)
            curr_frame = frames[i].astype(np.float32)
            next_frame = frames[i+1].astype(np.float32)
            
            # 时间平滑：当前帧为主，加入少量前后帧
            smoothed = (
                curr_frame * (1 - strength) + 
                (prev_frame + next_frame) * strength / 2
            )
            
            smoothed_frames.append(smoothed.astype(np.uint8))
    
    print("✅ 时间平滑完成")
    return smoothed_frames

def add_motion_blur(frames, blur_strength=1.5):
    """添加运动模糊效果"""
    print(f"\n💫 添加运动模糊 (强度: {blur_strength})...")
    
    blurred_frames = []
    
    for i, frame in enumerate(tqdm(frames, desc="运动模糊")):
        # 转换为PIL图像
        pil_img = Image.fromarray(frame)
        
        # 应用轻微的运动模糊
        blurred = pil_img.filter(ImageFilter.GaussianBlur(radius=blur_strength))
        
        # 与原图混合
        blended = Image.blend(pil_img, blurred, 0.3)
        
        blurred_frames.append(np.array(blended))
    
    print("✅ 运动模糊添加完成")
    return blurred_frames

def interpolate_frames(frames, interpolation_factor=2):
    """在帧之间插入中间帧"""
    print(f"\n🔄 帧插值 (插值倍数: {interpolation_factor})...")
    
    if interpolation_factor <= 1:
        return frames
    
    interpolated_frames = []
    
    for i in tqdm(range(len(frames) - 1), desc="帧插值"):
        current_frame = frames[i].astype(np.float32)
        next_frame = frames[i + 1].astype(np.float32)
        
        # 添加当前帧
        interpolated_frames.append(frames[i])
        
        # 在当前帧和下一帧之间插入中间帧
        for j in range(1, interpolation_factor):
            weight = j / interpolation_factor
            interpolated = (1 - weight) * current_frame + weight * next_frame
            interpolated_frames.append(interpolated.astype(np.uint8))
    
    # 添加最后一帧
    interpolated_frames.append(frames[-1])
    
    print(f"✅ 帧插值完成，帧数: {len(frames)} -> {len(interpolated_frames)}")
    return interpolated_frames

def create_smooth_video(frames, output_path, fps=24, processing_type="smooth"):
    """创建平滑视频"""
    print(f"\n🎬 创建平滑视频 ({processing_type})...")
    
    if not frames:
        print("❌ 没有可用帧")
        return False
    
    duration = len(frames) / fps
    
    print(f"📊 视频参数:")
    print(f"   帧数: {len(frames)}")
    print(f"   帧率: {fps} FPS")
    print(f"   时长: {duration:.2f} 秒")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="写入视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    print("💡 解决方案:")
    print("1. 时间平滑 - 减少帧间跳跃")
    print("2. 运动模糊 - 增加自然感")
    print("3. 帧插值 - 提高流畅度")
    print("4. 调整帧率 - 优化播放速度")
    
    # 加载帧
    frames = load_converted_frames()
    if not frames:
        return
    
    print(f"\n🎬 创建多个版本进行对比:")
    
    # 版本1: 原始帧 + 24fps
    print("\n📹 版本1: 原始帧 24fps")
    output1 = os.path.join(ROOT_DIR, "outputs/video_processing/smooth_v1_original_24fps.mp4")
    create_smooth_video(frames, output1, fps=24, processing_type="原始24fps")
    
    # 版本2: 时间平滑 + 24fps
    print("\n📹 版本2: 时间平滑 24fps")
    smoothed_frames = apply_temporal_smoothing(frames.copy(), strength=0.3)
    output2 = os.path.join(ROOT_DIR, "outputs/video_processing/smooth_v2_temporal_24fps.mp4")
    create_smooth_video(smoothed_frames, output2, fps=24, processing_type="时间平滑24fps")
    
    # 版本3: 时间平滑 + 运动模糊 + 24fps
    print("\n📹 版本3: 时间平滑 + 运动模糊 24fps")
    blurred_frames = add_motion_blur(smoothed_frames.copy(), blur_strength=1.0)
    output3 = os.path.join(ROOT_DIR, "outputs/video_processing/smooth_v3_blur_24fps.mp4")
    create_smooth_video(blurred_frames, output3, fps=24, processing_type="平滑+模糊24fps")
    
    # 版本4: 帧插值 + 48fps
    print("\n📹 版本4: 帧插值 48fps")
    interpolated_frames = interpolate_frames(smoothed_frames.copy(), interpolation_factor=2)
    output4 = os.path.join(ROOT_DIR, "outputs/video_processing/smooth_v4_interpolated_48fps.mp4")
    create_smooth_video(interpolated_frames, output4, fps=48, processing_type="插值48fps")
    
    # 版本5: 慢速播放 12fps
    print("\n📹 版本5: 时间平滑 12fps (慢速)")
    output5 = os.path.join(ROOT_DIR, "outputs/video_processing/smooth_v5_slow_12fps.mp4")
    create_smooth_video(smoothed_frames, output5, fps=12, processing_type="慢速12fps")
    
    print(f"\n🎉 所有版本创建完成!")
    print(f"\n📁 输出文件:")
    print(f"1. smooth_v1_original_24fps.mp4 - 原始帧24fps")
    print(f"2. smooth_v2_temporal_24fps.mp4 - 时间平滑24fps")
    print(f"3. smooth_v3_blur_24fps.mp4 - 平滑+模糊24fps")
    print(f"4. smooth_v4_interpolated_48fps.mp4 - 插值48fps")
    print(f"5. smooth_v5_slow_12fps.mp4 - 慢速12fps")
    
    print(f"\n💡 建议测试顺序:")
    print(f"1. 先看版本2 (时间平滑) - 应该减少跳跃感")
    print(f"2. 再看版本3 (加运动模糊) - 应该更自然")
    print(f"3. 试试版本4 (插值48fps) - 应该最流畅")
    print(f"4. 如果还是太快，试版本5 (12fps)")
    
    print(f"\n🎯 预期效果:")
    print(f"- 版本2/3: 减少幻灯片效果")
    print(f"- 版本4: 最流畅，像真正的视频")
    print(f"- 版本5: 慢速播放，适合欣赏")

if __name__ == "__main__":
    main()
