import cv2
import os
import glob
import requests
import replicate
from PIL import Image
import numpy as np
from tqdm import tqdm
import time

print("=== 完整重新处理视频 ===")

WORK_DIR = "outputs/video_processing"

def extract_all_frames_properly(video_path, output_dir):
    """正确提取所有帧 - 不跳帧"""
    os.makedirs(output_dir, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频文件")
        return False, 0
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    print(f"📹 视频信息:")
    print(f"   帧率: {fps} FPS")
    print(f"   总帧数: {total_frames}")
    print(f"   时长: {duration:.2f} 秒")
    
    print(f"\n🎬 提取所有帧到: {output_dir}")
    
    frame_count = 0
    success_count = 0
    
    with tqdm(total=total_frames, desc="提取帧") as pbar:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            frame_filename = f"frame_{frame_count:04d}.png"
            frame_path = os.path.join(output_dir, frame_filename)
            
            # 调整帧大小以减少处理时间
            frame_resized = cv2.resize(frame, (512, 512))
            
            if cv2.imwrite(frame_path, frame_resized):
                success_count += 1
            
            pbar.update(1)
    
    cap.release()
    
    print(f"✅ 帧提取完成:")
    print(f"   成功提取: {success_count}/{total_frames} 帧")
    print(f"   成功率: {success_count/total_frames*100:.1f}%")
    
    return success_count > 0, success_count

def save_flux_output(output, output_path):
    """保存 FLUX 输出到文件"""
    try:
        if hasattr(output, 'url'):
            response = requests.get(output.url, timeout=60)
            if response.status_code == 200:
                with open(output_path, "wb") as f:
                    f.write(response.content)
                return True
        elif isinstance(output, str) and output.startswith('http'):
            response = requests.get(output, timeout=60)
            if response.status_code == 200:
                with open(output_path, "wb") as f:
                    f.write(response.content)
                return True
        return False
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def process_frames_with_better_strategy(frames_dir, output_dir, style="ghibli", process_all=True):
    """改进的帧处理策略 - 处理所有帧以保持完整视频"""

    os.makedirs(output_dir, exist_ok=True)
    frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))

    total_frames = len(frame_files)
    print(f"📂 总共需要处理 {total_frames} 帧")

    if process_all:
        print(f"🎯 完整处理模式: 转换所有 {total_frames} 帧以保持原始视频流畅度")
        print(f"💰 预估成本: ${total_frames * 0.01:.2f}")
        print(f"⏱️ 预估时间: {total_frames * 8 / 60:.0f} 分钟")

        confirm = input(f"\n确认处理所有 {total_frames} 帧？(y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消处理")
            return []
    else:
        # 仅在用户明确要求时才采样
        print(f"⚠️ 注意: 采样模式会降低视频流畅度")
        max_frames = 100
        indices = np.linspace(0, total_frames-1, max_frames, dtype=int)
        frame_files = [frame_files[i] for i in indices]
        total_frames = len(frame_files)
        print(f"✅ 采样后帧数: {total_frames}")
    
    # 检查已处理的帧
    existing_files = glob.glob(os.path.join(output_dir, f"flux_{style}_*.png"))
    start_frame = len(existing_files)
    
    if start_frame > 0:
        print(f"🔄 发现已处理 {start_frame} 帧，从第 {start_frame + 1} 帧继续")
        user_input = input("是否继续处理剩余帧？(y/n): ").strip().lower()
        if user_input != 'y':
            return []
    
    # 风格提示词
    style_prompts = {
        "ghibli": "Transform this into Studio Ghibli anime style, Hayao Miyazaki art, soft watercolor painting, magical atmosphere, hand-drawn animation, whimsical",
        "shinkai": "Convert this to Makoto Shinkai anime style, Your Name movie style, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic",
        "anime": "Make this into high quality anime style, cel shading, vibrant colors, traditional Japanese animation, detailed art",
    }
    
    selected_prompt = style_prompts.get(style, style_prompts["ghibli"])
    print(f"🎨 使用风格: {style.upper()}")
    
    processed_frames = []
    success_count = start_frame
    failed_frames = []
    consecutive_failures = 0
    
    # 处理帧
    for i in range(start_frame, total_frames):
        frame_path = frame_files[i]
        
        try:
            print(f"\n🖼️ 处理帧 {i+1}/{total_frames}: {os.path.basename(frame_path)}")
            print(f"📊 进度: {((i+1)/total_frames)*100:.1f}%")
            
            # API 调用
            output = replicate.run(
                "black-forest-labs/flux-kontext-pro",
                input={
                    "prompt": selected_prompt,
                    "input_image": open(frame_path, "rb"),
                    "aspect_ratio": "match_input_image",
                    "output_format": "jpg",
                    "safety_tolerance": 2
                }
            )
            
            if output:
                # 保存文件
                output_filename = f"flux_{style}_{i+1:04d}.jpg"
                output_path = os.path.join(output_dir, output_filename)
                
                save_success = save_flux_output(output, output_path)
                
                if save_success and os.path.exists(output_path):
                    # 验证和处理图片
                    try:
                        img = Image.open(output_path)
                        img = img.resize((512, 512), Image.LANCZOS)
                        
                        # 保存为 PNG
                        png_path = output_path.replace('.jpg', '.png')
                        img.save(png_path)
                        
                        success_count += 1
                        consecutive_failures = 0  # 重置连续失败计数
                        print(f"✅ 帧 {i+1} 处理成功 ({success_count}/{total_frames})")
                        
                        # 预估剩余时间
                        if i > start_frame:
                            avg_time_per_frame = 8
                            remaining_frames = total_frames - (i + 1)
                            estimated_time = remaining_frames * avg_time_per_frame / 60
                            print(f"⏱️ 预计剩余时间: {estimated_time:.1f} 分钟")
                        
                    except Exception as e:
                        print(f"⚠️ 图片处理失败: {e}")
                        failed_frames.append(i+1)
                        consecutive_failures += 1
                        continue
                else:
                    print(f"❌ 文件保存失败")
                    failed_frames.append(i+1)
                    consecutive_failures += 1
                    continue
            else:
                print(f"❌ API 返回空结果")
                failed_frames.append(i+1)
                consecutive_failures += 1
                continue
            
            # 检查连续失败
            if consecutive_failures >= 5:
                print(f"⚠️ 连续失败 {consecutive_failures} 次，可能存在问题")
                continue_choice = input("是否继续处理？(y/n): ").strip().lower()
                if continue_choice != 'y':
                    break
                consecutive_failures = 0
            
            # API 限制等待
            time.sleep(3)
            
            # 每处理10帧显示统计
            if (i + 1) % 10 == 0:
                success_rate = success_count / (i + 1) * 100
                print(f"\n📈 阶段统计:")
                print(f"   已处理: {i+1}/{total_frames} 帧")
                print(f"   成功率: {success_rate:.1f}%")
                print(f"   失败帧: {len(failed_frames)}")
            
        except Exception as e:
            print(f"❌ 处理帧 {i+1} 失败: {e}")
            failed_frames.append(i+1)
            consecutive_failures += 1
            
            # 错误处理
            error_str = str(e).lower()
            if "rate limit" in error_str:
                print("⏳ 频率限制，等待60秒...")
                time.sleep(60)
            elif "timeout" in error_str:
                print("⏳ 超时，等待10秒...")
                time.sleep(10)
            else:
                time.sleep(5)
            
            continue
    
    # 收集所有成功处理的帧
    print(f"\n📋 收集处理结果...")
    all_processed_files = sorted(glob.glob(os.path.join(output_dir, f"flux_{style}_*.png")))
    
    for png_file in tqdm(all_processed_files, desc="加载处理后的帧"):
        try:
            img = Image.open(png_file)
            processed_frames.append(np.array(img))
        except Exception as e:
            print(f"⚠️ 加载帧失败: {png_file}, {e}")
    
    # 最终统计
    print(f"\n🎯 最终统计:")
    print(f"   总帧数: {total_frames}")
    print(f"   成功处理: {len(processed_frames)}")
    print(f"   成功率: {len(processed_frames)/total_frames*100:.1f}%")
    if failed_frames:
        print(f"   失败帧号: {failed_frames[:10]}{'...' if len(failed_frames) > 10 else ''}")
    
    return processed_frames

def create_final_video(frames, output_path, target_fps=30):
    """创建最终视频"""
    if len(frames) == 0:
        print("❌ 没有可用帧")
        return False
    
    duration = len(frames) / target_fps
    
    print(f"\n🎬 创建最终视频:")
    print(f"   帧数: {len(frames)}")
    print(f"   帧率: {target_fps} FPS")
    print(f"   时长: {duration:.2f} 秒")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="合成视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            return True
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    
    # 1. 找到原始视频
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    if not possible_videos:
        print("❌ 没有找到原始视频")
        return
    
    original_video = possible_videos[0]
    print(f"📹 原始视频: {os.path.basename(original_video)}")
    
    # 2. 检查 API Token
    if not os.environ.get("REPLICATE_API_TOKEN"):
        print("❌ 请设置 REPLICATE_API_TOKEN")
        return
    
    # 3. 重新提取所有帧
    frames_dir = f"{WORK_DIR}/frames_complete"
    print(f"\n🚀 步骤1: 重新提取所有帧")
    
    success, frame_count = extract_all_frames_properly(original_video, frames_dir)
    if not success:
        print("❌ 帧提取失败")
        return
    
    # 4. 估算处理成本和时间 - 完整处理所有帧
    estimated_time = frame_count * 8 / 60  # 每帧约8秒
    estimated_cost = frame_count * 0.01  # 每帧约$0.01

    print(f"\n📊 完整处理预估:")
    print(f"   总帧数: {frame_count}")
    print(f"   预计时间: {estimated_time:.0f} 分钟 ({estimated_time/60:.1f} 小时)")
    print(f"   预计成本: ${estimated_cost:.2f}")
    print(f"   目标: 完整转换所有帧，保持原始视频流畅度")

    print(f"\n💡 处理选项:")
    print(f"   A - 完整处理所有 {frame_count} 帧 (推荐，质量最好)")
    print(f"   B - 采样处理 100 帧 (快速预览)")
    print(f"   C - 取消处理")

    choice = input(f"\n请选择 (A/B/C): ").strip().upper()

    if choice == 'C':
        print("已取消处理")
        return
    elif choice == 'B':
        process_all = False
        print("⚠️ 选择采样模式，视频流畅度会降低")
    else:
        process_all = True
        print("✅ 选择完整处理模式")

    # 5. 重新转换帧
    style = "ghibli"
    output_dir = f"{WORK_DIR}/flux_{style}_reprocessed"

    print(f"\n🚀 步骤2: 重新转换帧")
    processed_frames = process_frames_with_better_strategy(
        frames_dir, output_dir, style, process_all
    )
    
    # 6. 创建最终视频
    if len(processed_frames) > 0:
        output_video = f"{WORK_DIR}/reprocessed_complete_video.mp4"
        
        print(f"\n🚀 步骤3: 创建最终视频")
        success = create_final_video(processed_frames, output_video, target_fps=30)
        
        if success:
            print(f"\n🎉 完整重新处理成功!")
            print(f"🎬 输出: {output_video}")
        else:
            print(f"\n❌ 视频创建失败")
    else:
        print(f"\n❌ 没有成功处理任何帧")

if __name__ == "__main__":
    main()
