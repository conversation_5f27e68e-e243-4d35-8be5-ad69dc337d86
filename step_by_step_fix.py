import cv2
import os
import glob
import numpy as np
from PIL import Image

print("=== 分步修复视频帧率问题 ===")

WORK_DIR = "outputs/video_processing"

def step1_analyze_current_situation():
    """步骤1: 分析当前情况"""
    print("🔍 步骤1: 分析当前情况")
    
    # 分析原始视频
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    if not possible_videos:
        print("❌ 没有找到原始视频")
        return None
    
    original_video = possible_videos[0]
    cap = cv2.VideoCapture(original_video)
    
    if cap.isOpened():
        orig_fps = cap.get(cv2.CAP_PROP_FPS)
        orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        orig_duration = orig_frames / orig_fps
        cap.release()
        
        print(f"📹 原始视频:")
        print(f"   文件: {os.path.basename(original_video)}")
        print(f"   帧率: {orig_fps} FPS")
        print(f"   总帧数: {orig_frames}")
        print(f"   时长: {orig_duration:.2f} 秒")
    else:
        print("❌ 无法读取原始视频")
        return None
    
    # 检查提取的帧
    frames_dirs = [
        f"{WORK_DIR}/frames_part1",
        f"{WORK_DIR}/frames_complete"
    ]
    
    extracted_count = 0
    frames_dir = None
    
    for dir_path in frames_dirs:
        if os.path.exists(dir_path):
            frame_files = glob.glob(os.path.join(dir_path, "*.png"))
            if frame_files:
                extracted_count = len(frame_files)
                frames_dir = dir_path
                print(f"📂 提取的帧:")
                print(f"   目录: {dir_path}")
                print(f"   数量: {extracted_count}")
                print(f"   提取率: {extracted_count/orig_frames*100:.1f}%")
                break
    
    # 检查转换的帧
    flux_dirs = [
        f"{WORK_DIR}/flux_ghibli",
        f"{WORK_DIR}/flux_ghibli_full",
        f"{WORK_DIR}/flux_ghibli_reprocessed"
    ]
    
    converted_count = 0
    flux_dir = None
    
    for dir_path in flux_dirs:
        if os.path.exists(dir_path):
            flux_files = glob.glob(os.path.join(dir_path, "flux_*.png"))
            if not flux_files:
                flux_files = glob.glob(os.path.join(dir_path, "flux_*.jpg"))
            
            if flux_files:
                converted_count = len(flux_files)
                flux_dir = dir_path
                print(f"🎨 转换的帧:")
                print(f"   目录: {dir_path}")
                print(f"   数量: {converted_count}")
                print(f"   转换率: {converted_count/orig_frames*100:.1f}%")
                break
    
    # 问题总结
    print(f"\n📊 问题总结:")
    print(f"   原始帧数: {orig_frames}")
    print(f"   提取帧数: {extracted_count} (丢失 {orig_frames-extracted_count} 帧)")
    print(f"   转换帧数: {converted_count} (丢失 {orig_frames-converted_count} 帧)")
    print(f"   总体损失: {(1-converted_count/orig_frames)*100:.1f}%")
    
    return {
        'original_video': original_video,
        'orig_fps': orig_fps,
        'orig_frames': orig_frames,
        'orig_duration': orig_duration,
        'extracted_count': extracted_count,
        'converted_count': converted_count,
        'frames_dir': frames_dir,
        'flux_dir': flux_dir
    }

def step2_recommend_solution(info):
    """步骤2: 推荐解决方案"""
    print("\n🛠️ 步骤2: 推荐解决方案")
    
    orig_frames = info['orig_frames']
    extracted_count = info['extracted_count']
    converted_count = info['converted_count']
    
    print(f"根据分析结果，推荐以下解决方案:")
    
    if converted_count < orig_frames * 0.3:  # 转换率低于30%
        print(f"\n🔴 方案A: 完全重新处理 (推荐)")
        print(f"   原因: 转换率太低 ({converted_count/orig_frames*100:.1f}%)")
        print(f"   步骤: 重新提取帧 → 重新AI转换 → 生成视频")
        print(f"   优点: 质量最好，帧率完整")
        print(f"   缺点: 时间长，成本高")
        print(f"   预估成本: ${orig_frames * 0.01:.2f}")
        print(f"   预估时间: {orig_frames * 8 / 60:.0f} 分钟")
        
    elif converted_count < orig_frames * 0.7:  # 转换率30-70%
        print(f"\n🟡 方案B: 补充转换缺失帧")
        print(f"   原因: 转换率中等 ({converted_count/orig_frames*100:.1f}%)")
        print(f"   步骤: 找出缺失帧 → 转换缺失帧 → 合并生成视频")
        print(f"   优点: 成本较低，保留已有成果")
        print(f"   缺点: 可能存在不一致性")
        missing_frames = orig_frames - converted_count
        print(f"   预估成本: ${missing_frames * 0.01:.2f}")
        print(f"   预估时间: {missing_frames * 8 / 60:.0f} 分钟")
        
    else:  # 转换率70%以上
        print(f"\n🟢 方案C: 插值修复")
        print(f"   原因: 转换率较高 ({converted_count/orig_frames*100:.1f}%)")
        print(f"   步骤: 智能插值缺失帧 → 时间平滑 → 生成视频")
        print(f"   优点: 快速，成本低")
        print(f"   缺点: 插值帧质量可能不如原生转换")
        print(f"   预估成本: $0 (无需API调用)")
        print(f"   预估时间: 5-10 分钟")
    
    # 临时方案
    print(f"\n🔵 方案D: 临时快速修复")
    print(f"   用途: 快速预览效果")
    print(f"   步骤: 重复现有帧 → 生成预览视频")
    print(f"   优点: 立即可用")
    print(f"   缺点: 质量差，仅供预览")
    print(f"   预估时间: 1-2 分钟")

def step3_extract_frames_properly(video_path, output_dir):
    """步骤3: 正确提取所有帧"""
    print(f"\n🎬 步骤3: 重新提取所有帧")
    
    os.makedirs(output_dir, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频文件")
        return False
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"📹 开始提取 {total_frames} 帧...")
    
    frame_count = 0
    success_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整大小以适合AI处理
        frame_resized = cv2.resize(frame, (512, 512))
        
        frame_filename = f"frame_{frame_count:04d}.png"
        frame_path = os.path.join(output_dir, frame_filename)
        
        if cv2.imwrite(frame_path, frame_resized):
            success_count += 1
        
        # 显示进度
        if frame_count % 50 == 0:
            print(f"   进度: {frame_count}/{total_frames} ({frame_count/total_frames*100:.1f}%)")
    
    cap.release()
    
    print(f"✅ 帧提取完成:")
    print(f"   成功: {success_count}/{total_frames}")
    print(f"   成功率: {success_count/total_frames*100:.1f}%")
    
    return success_count == total_frames

def step4_create_preview_video(info):
    """步骤4: 创建预览视频 (使用现有帧)"""
    print(f"\n🎥 步骤4: 创建预览视频")
    
    flux_dir = info['flux_dir']
    if not flux_dir:
        print("❌ 没有找到转换后的帧")
        return False
    
    # 加载现有帧
    flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_*.png")))
    if not flux_files:
        flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_*.jpg")))
    
    if not flux_files:
        print("❌ 没有找到转换帧文件")
        return False
    
    print(f"📂 找到 {len(flux_files)} 个转换帧")
    
    # 加载帧
    frames = []
    for frame_path in flux_files:
        try:
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            frames.append(np.array(img))
        except Exception as e:
            print(f"⚠️ 加载失败: {frame_path}")
            continue
    
    if not frames:
        print("❌ 无法加载任何帧")
        return False
    
    # 扩展帧数以匹配原始时长
    orig_frames = info['orig_frames']
    orig_fps = info['orig_fps']
    current_count = len(frames)
    
    # 计算重复倍数
    repeat_factor = max(1, orig_frames // current_count)
    
    print(f"🔧 扩展帧数:")
    print(f"   当前帧数: {current_count}")
    print(f"   目标帧数: {orig_frames}")
    print(f"   重复倍数: {repeat_factor}")
    
    # 重复帧
    extended_frames = []
    for frame in frames:
        for _ in range(repeat_factor):
            extended_frames.append(frame)
    
    # 补充到目标帧数
    while len(extended_frames) < orig_frames:
        extended_frames.append(frames[-1])
    
    # 截断到目标帧数
    extended_frames = extended_frames[:orig_frames]
    
    print(f"   最终帧数: {len(extended_frames)}")
    
    # 创建视频
    output_path = f"{WORK_DIR}/preview_video.mp4"
    
    try:
        height, width = extended_frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, orig_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        print("🎬 生成预览视频...")
        for i, frame in enumerate(extended_frames):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
            
            if (i + 1) % 50 == 0:
                print(f"   进度: {i+1}/{len(extended_frames)} ({(i+1)/len(extended_frames)*100:.1f}%)")
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            duration = len(extended_frames) / orig_fps
            print(f"✅ 预览视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            print(f"🎯 帧率: {orig_fps} FPS")
            return True
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    
    # 步骤1: 分析当前情况
    info = step1_analyze_current_situation()
    if not info:
        return
    
    # 步骤2: 推荐解决方案
    step2_recommend_solution(info)
    
    # 用户选择
    print(f"\n🤔 你想执行哪个方案？")
    print("A - 完全重新处理 (质量最好)")
    print("B - 补充转换缺失帧 (平衡方案)")  
    print("C - 插值修复 (快速方案)")
    print("D - 创建预览视频 (立即可用)")
    print("Q - 退出")
    
    choice = input("\n请选择 (A/B/C/D/Q): ").strip().upper()
    
    if choice == 'A':
        print(f"\n🚀 执行方案A: 完全重新处理")
        print("请运行: python complete_reprocess.py")
        
    elif choice == 'B':
        print(f"\n🚀 执行方案B: 补充转换缺失帧")
        print("此方案需要自定义实现，建议先选择方案D查看效果")
        
    elif choice == 'C':
        print(f"\n🚀 执行方案C: 插值修复")
        print("请运行: python fix_video_smoothness.py")
        
    elif choice == 'D':
        print(f"\n🚀 执行方案D: 创建预览视频")
        success = step4_create_preview_video(info)
        if success:
            print(f"\n🎉 预览视频创建完成!")
            print(f"📁 文件位置: {WORK_DIR}/preview_video.mp4")
            print(f"\n💡 提示: 这是使用现有帧的预览版本")
            print(f"如果效果可以接受，可以考虑方案C进行优化")
            print(f"如果需要更好质量，建议选择方案A完全重新处理")
        else:
            print(f"\n❌ 预览视频创建失败")
            
    elif choice == 'Q':
        print("👋 退出程序")
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
