import cv2
import os
import glob
import numpy as np
from PIL import Image
from tqdm import tqdm

print("=== 修复视频帧率不匹配问题 ===")

WORK_DIR = "outputs/video_processing"

def get_original_video_info():
    """获取原始视频信息"""
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    
    if possible_videos:
        original_video = possible_videos[0]
        cap = cv2.VideoCapture(original_video)
        if cap.isOpened():
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            cap.release()
            
            print(f"📹 原始视频信息:")
            print(f"   文件: {os.path.basename(original_video)}")
            print(f"   帧率: {fps} FPS")
            print(f"   总帧数: {total_frames}")
            print(f"   时长: {duration:.2f} 秒")
            
            return fps, total_frames, duration, original_video
    
    print("❌ 未找到原始视频")
    return None, None, None, None

def extract_all_frames_properly(video_path, output_dir):
    """正确提取所有帧"""
    os.makedirs(output_dir, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频文件")
        return False
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"\n🎬 提取所有帧到: {output_dir}")
    print(f"   总帧数: {total_frames}")
    
    frame_count = 0
    success_count = 0
    
    with tqdm(total=total_frames, desc="提取帧") as pbar:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            frame_filename = f"frame_{frame_count:04d}.png"
            frame_path = os.path.join(output_dir, frame_filename)
            
            # 保存帧
            if cv2.imwrite(frame_path, frame):
                success_count += 1
            
            pbar.update(1)
    
    cap.release()
    
    print(f"✅ 帧提取完成:")
    print(f"   成功提取: {success_count}/{total_frames} 帧")
    print(f"   成功率: {success_count/total_frames*100:.1f}%")
    
    return success_count > 0

def load_converted_frames(flux_dir):
    """加载已转换的帧"""
    flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.png")))
    if not flux_files:
        flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.jpg")))
    
    if not flux_files:
        print("❌ 没有找到转换后的帧")
        return [], []
    
    print(f"🎨 找到 {len(flux_files)} 个转换帧")
    
    frames = []
    frame_numbers = []
    
    for frame_path in tqdm(flux_files, desc="加载转换帧"):
        try:
            # 提取帧号
            base_name = os.path.basename(frame_path)
            frame_num = int(base_name.split('_')[-1].split('.')[0])
            
            # 加载图像
            img = Image.open(frame_path)
            img = img.resize((512, 512), Image.LANCZOS)
            
            frames.append(np.array(img))
            frame_numbers.append(frame_num)
            
        except Exception as e:
            print(f"⚠️ 加载失败: {frame_path}, {e}")
            continue
    
    return frames, frame_numbers

def create_frame_interpolation(frames, frame_numbers, target_frame_count):
    """创建帧插值以匹配目标帧数"""
    if len(frames) == 0:
        return []
    
    print(f"\n🔧 帧插值处理:")
    print(f"   当前帧数: {len(frames)}")
    print(f"   目标帧数: {target_frame_count}")
    
    if len(frames) >= target_frame_count:
        # 如果帧数够多，直接返回
        return frames[:target_frame_count]
    
    # 需要插值
    interpolated_frames = []
    
    # 计算插值比例
    ratio = (target_frame_count - 1) / (len(frames) - 1) if len(frames) > 1 else 1
    
    for i in range(target_frame_count):
        # 计算对应的原始帧索引
        orig_index = i / ratio
        
        if orig_index >= len(frames) - 1:
            # 超出范围，使用最后一帧
            interpolated_frames.append(frames[-1])
        elif orig_index == int(orig_index):
            # 正好对应原始帧
            interpolated_frames.append(frames[int(orig_index)])
        else:
            # 需要插值
            idx1 = int(orig_index)
            idx2 = min(idx1 + 1, len(frames) - 1)
            weight = orig_index - idx1
            
            # 线性插值
            frame1 = frames[idx1].astype(np.float32)
            frame2 = frames[idx2].astype(np.float32)
            interpolated = (1 - weight) * frame1 + weight * frame2
            interpolated_frames.append(interpolated.astype(np.uint8))
    
    print(f"✅ 插值完成，生成 {len(interpolated_frames)} 帧")
    return interpolated_frames

def create_matched_video(frames, output_path, target_fps, target_duration):
    """创建匹配原始视频的视频"""
    if len(frames) == 0:
        print("❌ 没有可用帧")
        return False
    
    actual_duration = len(frames) / target_fps
    
    print(f"\n🎬 创建匹配视频:")
    print(f"   帧数: {len(frames)}")
    print(f"   帧率: {target_fps} FPS")
    print(f"   实际时长: {actual_duration:.2f} 秒")
    print(f"   目标时长: {target_duration:.2f} 秒")
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False
        
        for frame in tqdm(frames, desc="生成视频"):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)
        
        out.release()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {actual_duration:.2f} 秒")
            return True
        else:
            print("❌ 视频文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def main():
    """主程序"""
    
    # 1. 获取原始视频信息
    orig_fps, orig_frames, orig_duration, orig_video = get_original_video_info()
    if not orig_video:
        return
    
    # 2. 检查是否需要重新提取帧
    frames_dir = f"{WORK_DIR}/frames_complete"
    extracted_frames = glob.glob(os.path.join(frames_dir, "*.png"))
    
    if len(extracted_frames) != orig_frames:
        print(f"\n⚠️ 帧提取不完整 ({len(extracted_frames)}/{orig_frames})")
        print("🔄 重新提取所有帧...")
        
        if not extract_all_frames_properly(orig_video, frames_dir):
            print("❌ 帧提取失败")
            return
    else:
        print(f"✅ 帧提取完整 ({len(extracted_frames)} 帧)")
    
    # 3. 查找转换后的帧
    possible_flux_dirs = [
        f"{WORK_DIR}/flux_ghibli",
        f"{WORK_DIR}/flux_ghibli_full",
        f"{WORK_DIR}/flux_anime"
    ]
    
    flux_dir = None
    for dir_path in possible_flux_dirs:
        if os.path.exists(dir_path):
            files = glob.glob(os.path.join(dir_path, "flux_*.*"))
            if files:
                flux_dir = dir_path
                break
    
    if not flux_dir:
        print("❌ 没有找到转换后的帧")
        return
    
    print(f"✅ 使用转换帧目录: {flux_dir}")
    
    # 4. 加载转换后的帧
    converted_frames, frame_numbers = load_converted_frames(flux_dir)
    
    if not converted_frames:
        print("❌ 无法加载转换帧")
        return
    
    # 5. 创建插值帧以匹配原始帧数
    target_frame_count = orig_frames
    final_frames = create_frame_interpolation(converted_frames, frame_numbers, target_frame_count)
    
    # 6. 创建匹配的视频
    output_video = f"{WORK_DIR}/fixed_framerate_video.mp4"
    success = create_matched_video(final_frames, output_video, orig_fps, orig_duration)
    
    if success:
        print(f"\n🎉 帧率修复完成!")
        print(f"📁 输出文件: {output_video}")
        print(f"🎯 视频参数: {orig_fps} FPS, {orig_duration:.2f} 秒")
    else:
        print(f"\n❌ 帧率修复失败")

if __name__ == "__main__":
    main()
