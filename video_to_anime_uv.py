import replicate
import os
import glob
import requests
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm
import time

print("=== FLUX Kontext Pro 完整视频处理 ===")

WORK_DIR = "outputs/video_processing"

def save_flux_output(output, output_path):
    """保存 FLUX 输出到文件"""
    try:
        if hasattr(output, 'url'):
            response = requests.get(output.url, timeout=60)
            if response.status_code == 200:
                with open(output_path, "wb") as f:
                    f.write(response.content)
                return True
        elif isinstance(output, str) and output.startswith('http'):
            response = requests.get(output, timeout=60)
            if response.status_code == 200:
                with open(output_path, "wb") as f:
                    f.write(response.content)
                return True
        return False
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def process_all_frames_with_flux(frames_dir, output_dir, style="ghibli"):
    """处理所有帧"""
    
    os.makedirs(output_dir, exist_ok=True)
    frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))
    
    total_frames = len(frame_files)
    print(f"📂 总共需要处理 {total_frames} 帧")
    
    # 检查已处理的帧
    existing_files = glob.glob(os.path.join(output_dir, "flux_*.png"))
    start_frame = len(existing_files)
    
    if start_frame > 0:
        print(f"🔄 发现已处理 {start_frame} 帧，从第 {start_frame + 1} 帧继续")
        user_input = input("是否继续处理剩余帧？(y/n): ").strip().lower()
        if user_input != 'y':
            return []
    
    # 风格提示词
    style_prompts = {
        "ghibli": "Transform this into Studio Ghibli anime style, Hayao Miyazaki art, soft watercolor painting, magical atmosphere, hand-drawn animation, whimsical",
        "shinkai": "Convert this to Makoto Shinkai anime style, Your Name movie style, beautiful detailed sky, dramatic lighting, photorealistic anime, cinematic",
        "anime": "Make this into high quality anime style, cel shading, vibrant colors, traditional Japanese animation, detailed art",
    }
    
    selected_prompt = style_prompts.get(style, style_prompts["ghibli"])
    print(f"🎨 使用风格: {style.upper()}")
    print(f"💬 提示词: {selected_prompt}")
    
    processed_frames = []
    success_count = start_frame  # 从已有的开始计数
    failed_frames = []
    
    # 处理所有帧（从未处理的开始）
    for i in range(start_frame, total_frames):
        frame_path = frame_files[i]
        
        try:
            print(f"\n🖼️ 处理帧 {i+1}/{total_frames}: {os.path.basename(frame_path)}")
            print(f"📊 进度: {((i+1)/total_frames)*100:.1f}%")
            
            # API 调用
            output = replicate.run(
                "black-forest-labs/flux-kontext-pro",
                input={
                    "prompt": selected_prompt,
                    "input_image": open(frame_path, "rb"),
                    "aspect_ratio": "match_input_image",
                    "output_format": "jpg",
                    "safety_tolerance": 2
                }
            )
            
            if output:
                # 保存文件
                output_filename = f"flux_{style}_{i+1:04d}.jpg"
                output_path = os.path.join(output_dir, output_filename)
                
                save_success = save_flux_output(output, output_path)
                
                if save_success and os.path.exists(output_path):
                    # 验证和处理图片
                    try:
                        img = Image.open(output_path)
                        img = img.resize((512, 512), Image.LANCZOS)
                        
                        # 保存为 PNG
                        png_path = output_path.replace('.jpg', '.png')
                        img.save(png_path)
                        
                        success_count += 1
                        print(f"✅ 帧 {i+1} 处理成功 ({success_count}/{total_frames})")
                        
                        # 预估剩余时间
                        if i > start_frame:
                            avg_time_per_frame = 8  # 大约每帧8秒
                            remaining_frames = total_frames - (i + 1)
                            estimated_time = remaining_frames * avg_time_per_frame / 60
                            print(f"⏱️ 预计剩余时间: {estimated_time:.1f} 分钟")
                        
                    except Exception as e:
                        print(f"⚠️ 图片处理失败: {e}")
                        failed_frames.append(i+1)
                        continue
                else:
                    print(f"❌ 文件保存失败")
                    failed_frames.append(i+1)
                    continue
            else:
                print(f"❌ API 返回空结果")
                failed_frames.append(i+1)
                continue
            
            # API 限制等待（缩短等待时间以加快处理）
            time.sleep(2)
            
            # 每处理10帧显示一次统计
            if (i + 1) % 10 == 0:
                success_rate = success_count / (i + 1) * 100
                print(f"\n📈 阶段统计:")
                print(f"   已处理: {i+1}/{total_frames} 帧")
                print(f"   成功率: {success_rate:.1f}%")
                print(f"   失败帧: {len(failed_frames)}")
            
        except Exception as e:
            print(f"❌ 处理帧 {i+1} 失败: {e}")
            failed_frames.append(i+1)
            
            # 错误处理
            error_str = str(e).lower()
            if "rate limit" in error_str:
                print("⏳ 频率限制，等待60秒...")
                time.sleep(60)
            elif "timeout" in error_str:
                print("⏳ 超时，等待10秒...")
                time.sleep(10)
            else:
                time.sleep(5)
            
            continue
    
    # 收集所有成功处理的帧
    print(f"\n📋 收集处理结果...")
    all_processed_files = sorted(glob.glob(os.path.join(output_dir, "flux_*.png")))
    
    for png_file in tqdm(all_processed_files, desc="加载处理后的帧"):
        try:
            img = Image.open(png_file)
            processed_frames.append(np.array(img))
        except Exception as e:
            print(f"⚠️ 加载帧失败: {png_file}, {e}")
    
    # 最终统计
    print(f"\n🎯 最终统计:")
    print(f"   总帧数: {total_frames}")
    print(f"   成功处理: {len(processed_frames)}")
    print(f"   成功率: {len(processed_frames)/total_frames*100:.1f}%")
    if failed_frames:
        print(f"   失败帧号: {failed_frames[:10]}{'...' if len(failed_frames) > 10 else ''}")
    
    return processed_frames

def create_smooth_video(frames, output_path, target_fps=30, original_duration=None):
    """创建平滑的视频，支持帧重复以匹配原始时长"""
    if len(frames) == 0:
        print("❌ 没有可用帧")
        return False

    total_frames = len(frames)

    # 如果提供了原始时长，计算需要重复帧来匹配时长
    if original_duration:
        target_total_frames = int(target_fps * original_duration)
        repeat_factor = max(1, target_total_frames // total_frames)

        print(f"\n🎬 创建匹配时长的视频:")
        print(f"   原始帧数: {total_frames}")
        print(f"   目标时长: {original_duration:.2f} 秒")
        print(f"   目标帧率: {target_fps} FPS")
        print(f"   需要总帧数: {target_total_frames}")
        print(f"   帧重复倍数: {repeat_factor}")

        # 重复帧以匹配时长
        extended_frames = []
        for frame in frames:
            for _ in range(repeat_factor):
                extended_frames.append(frame)

        # 如果还不够，继续重复最后几帧
        while len(extended_frames) < target_total_frames:
            extended_frames.append(frames[-1])

        # 如果太多了，截断
        extended_frames = extended_frames[:target_total_frames]
        frames = extended_frames
        total_frames = len(frames)

    duration = total_frames / target_fps

    print(f"\n🎬 最终视频参数:")
    print(f"   帧数: {total_frames}")
    print(f"   帧率: {target_fps} FPS")
    print(f"   时长: {duration:.2f} 秒")

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))

        if not out.isOpened():
            print("❌ 无法创建视频文件")
            return False

        for i, frame in enumerate(tqdm(frames, desc="合成视频")):
            if len(frame.shape) == 3:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            else:
                frame_bgr = frame
            out.write(frame_bgr)

        out.release()

        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)
            print(f"✅ 视频创建成功!")
            print(f"📁 文件: {output_path}")
            print(f"💾 大小: {file_size:.2f} MB")
            print(f"⏱️ 时长: {duration:.2f} 秒")
            return True
        else:
            print("❌ 视频文件未生成")
            return False

    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return False

def get_original_video_info():
    """获取原始视频信息"""
    original_video = None
    possible_videos = glob.glob("inputs/animatediff/*.mp4")

    if possible_videos:
        original_video = possible_videos[0]
        cap = cv2.VideoCapture(original_video)
        if cap.isOpened():
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            cap.release()
            return fps, total_frames, duration

    return 30.0, 302, 10.07  # 默认值

def main():
    """主程序"""
    frames_dir = f"{WORK_DIR}/frames_part1"

    if not os.path.exists(frames_dir):
        print(f"❌ 帧目录不存在: {frames_dir}")
        return

    frame_count = len(glob.glob(os.path.join(frames_dir, "*.png")))
    print(f"📂 找到 {frame_count} 个原始帧")

    # 获取原始视频信息
    original_fps, original_frames, original_duration = get_original_video_info()
    print(f"📹 原始视频: {original_fps} FPS, {original_frames} 帧, {original_duration:.2f} 秒")

    # 检查 API Token
    if not os.environ.get("REPLICATE_API_TOKEN"):
        print("❌ 请设置 REPLICATE_API_TOKEN")
        return

    # 估算处理时间和成本
    estimated_time = frame_count * 8 / 60  # 每帧约8秒
    estimated_cost = frame_count * 0.01  # 每帧约$0.01

    print(f"\n📊 处理预估:")
    print(f"   预计时间: {estimated_time:.0f} 分钟")
    print(f"   预计成本: ${estimated_cost:.2f}")

    confirm = input(f"\n确认处理全部 {frame_count} 帧？(y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消处理")
        return

    # 处理风格
    style = "ghibli"
    output_dir = f"{WORK_DIR}/flux_{style}_full"
    output_video = f"{WORK_DIR}/part1_flux_{style}_complete.mp4"

    print(f"\n🚀 开始完整处理...")

    # 处理所有帧
    processed_frames = process_all_frames_with_flux(frames_dir, output_dir, style)

    # 创建视频
    if len(processed_frames) > 0:
        # 创建匹配原始时长的视频
        success = create_smooth_video(
            processed_frames,
            output_video,
            target_fps=30,  # 使用30fps而不是12fps
            original_duration=original_duration
        )
        if success:
            print(f"\n🎉 完整视频处理成功!")
            print(f"🎬 输出: {output_video}")
        else:
            print(f"\n❌ 视频创建失败")
    else:
        print(f"\n❌ 没有成功处理任何帧")

if __name__ == "__main__":
    main()