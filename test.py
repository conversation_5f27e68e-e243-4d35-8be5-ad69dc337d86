import cv2
import os
import glob
from PIL import Image

print("=== 视频帧率问题诊断 ===")

def diagnose_frame_mismatch():
    """诊断帧率不匹配问题"""
    
    # 1. 分析原始视频
    original_video = None
    possible_videos = glob.glob("inputs/animatediff/*.mp4")
    if possible_videos:
        original_video = possible_videos[0]
        
        cap = cv2.VideoCapture(original_video)
        if cap.isOpened():
            orig_fps = cap.get(cv2.CAP_PROP_FPS)
            orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            orig_duration = orig_frames / orig_fps
            
            print(f"📹 原始视频:")
            print(f"   帧率: {orig_fps} FPS")
            print(f"   总帧数: {orig_frames}")
            print(f"   时长: {orig_duration:.2f} 秒")
            cap.release()
        else:
            print("❌ 无法读取原始视频")
            return
    
    # 2. 分析提取的原始帧
    frames_dir = "outputs/video_processing/frames_part1"
    if os.path.exists(frames_dir):
        original_frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))
        print(f"\n📂 提取的原始帧:")
        print(f"   数量: {len(original_frame_files)}")
        
        if original_frame_files:
            first_frame = os.path.basename(original_frame_files[0])
            last_frame = os.path.basename(original_frame_files[-1])
            print(f"   范围: {first_frame} 到 {last_frame}")
    
    # 3. 分析转换后的帧
    flux_dirs = [
        "outputs/video_processing/flux_ghibli",
        "outputs/video_processing/flux_ghibli_full"
    ]
    
    for flux_dir in flux_dirs:
        if os.path.exists(flux_dir):
            flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.png")))
            if not flux_files:
                flux_files = sorted(glob.glob(os.path.join(flux_dir, "flux_ghibli_*.jpg")))
            
            if flux_files:
                print(f"\n🎨 转换后的帧 ({os.path.basename(flux_dir)}):")
                print(f"   数量: {len(flux_files)}")
                
                first_converted = os.path.basename(flux_files[0])
                last_converted = os.path.basename(flux_files[-1])
                print(f"   范围: {first_converted} 到 {last_converted}")
                
                # 检查连续性
                frame_numbers = []
                for f in flux_files:
                    try:
                        base_name = os.path.basename(f)
                        num = int(base_name.split('_')[-1].split('.')[0])
                        frame_numbers.append(num)
                    except:
                        continue
                
                if frame_numbers:
                    frame_numbers.sort()
                    missing = []
                    for i in range(frame_numbers[0], frame_numbers[-1] + 1):
                        if i not in frame_numbers:
                            missing.append(i)
                    
                    print(f"   帧号范围: {frame_numbers[0]} - {frame_numbers[-1]}")
                    print(f"   缺失帧数: {len(missing)}")
                    
                    if missing:
                        print(f"   缺失帧号: {missing[:20]}{'...' if len(missing) > 20 else ''}")
                    
                    # 计算如果按不同帧率播放的时长
                    for fps in [12, 24, 30]:
                        duration = len(flux_files) / fps
                        print(f"   {fps}fps播放时长: {duration:.2f}秒")
                
                break

if __name__ == "__main__":
    diagnose_frame_mismatch()